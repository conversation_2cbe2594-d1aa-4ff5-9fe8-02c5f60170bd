import requests
from bs4 import BeautifulSoup


# 抓取文章内容
def get_web_content(url):
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.42(0x18002a2a) NetType/4G Language/zh_CN',
    }
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.text, 'html.parser')

    title = soup.select_one("#activity-name").text.strip()
    content = soup.select_one("#js_content").text.strip()

    return title, content


# 获取故事大纲
def get_structure(url):
    title, content = get_web_content(url)

    prompt = """
    Role: 文章结构分析大师
    
    Background: 
    你是一位情感故事的专家,擅长分析文章风格并进行创新，创作出属于自己的故事大纲
    
    Skills:
    - 精通各类文体的语言风格和语法结构。
    - 遵循原文思路,内容连贯流畅。
    - 能准确抓取原文的核心观点并进行创新表达。
    
    Goals: 
    - 根据用户提供的内容适当修改部分情节以生成新的故事大纲。
    
    Constraints:
    - 人物介绍中保留人物的所有细节，包括家庭、出身、背景、职业等。
    - 对用户提供的故事在情节上进行修改，适当增添元素。
    - 在故事原背景下调整故事的主干情节。
    - 要突出表现人物的情感冲突和发展。
    - 对原故事大纲进行创新调整，调整后仍然以以下格式输出：
    + 人物介绍：
    + 小说背景：
    + 小说引入：
    + 小说发展：
    + 小说转折：
    + 小说结局：
    
    ## Workflow:
    1. 根据下面提供的故事内容进行创作，然后按照 Constraints 约定格式输出：
    
    """ + title + '\n\n' + content

    result = chatgpt.chat([
        {"role": "system", "content": "文章结构分析大师"},
        {"role": "user", "content": prompt},
    ])
    return result


def reprint(origin_content):
    prompt = """
    你是一位资深的情感故事写作家，擅长编写出引人入胜的情感故事，吸引爱看八卦的中老年人群。你将按照用户提供的内容撰写情感文章。
    作为一位敏感且熟练的作家，你的任务是"在根据提供的内容创作一篇富有情感深度和人性探索的故事。
    故事以第一人称写，故事开头要介绍主角的家庭背景和个人简介，要给角色取名，已有的名字要替换掉。
    故事需要使读者感同身受，引发他们对自己生活中类似问题的思考。编写一篇 1880 字的情感故事，以散文的文体为形式，以第一人称“我”的视角诉说。

    在写作过程中，请确保:
    - 故事开篇的时候请简单交代一个背景，，"我是谁?多少岁，来自哪里，做什么的?”，用一个人的反常行为和异常事件作构子，吸引读者好奇到底发生了什么事。
    - 对话真实，反映角色间的情感和关系
    - 使用简洁、生动、有力的语言表达情感
    - 设定了背景，诸如主人公的年龄，职业，收入等
    - 简洁明快:这段开头并没有过多的修饰，而是直截了当地介绍了背景与接下来的情节，这使得读者能迅速进入故事情境中生活化的细节: 如“每年收入近百万”“每月拿出5088元”等细节，让故事更具真实感
    - 使用了对比，情感描写等手法
    - 少量描述环境和场景
    - 利用短句，感叹句和反问句加强情感深度
    - 在情节高潮和转折点，用简短有力的句子凸显反转
    - 故事首尾应相呼应。
    - 适当使用略语和省略句加快叙述节秦
    - 利用夸张和情感化的修辞增强情感色彩。
    - 您可以扩展和创新故事内容，但确保情节有起伏，并符合您给出的大纲.
    - 全文要口语化（例如’与‘换成’跟‘，’什么‘换成’啥‘等等），第一人称视角讲述，例如：我是张磊，今年32岁，我... ；
    - 尽量增加更多的人物对话和内心活动。
    - 社会意义不用单独列出，作为最后收尾的段落点题。
    
    用户提供的内容：
    """ + origin_content

    result = chatgpt.chat([
        {"role": "system", "content": "情感故事写作家"},
        {"role": "user", "content": prompt},
    ])
    return result



print(get_web_content("https://mp.weixin.qq.com/s/6azBI8OauTtCUrcqWCQM_w"))


