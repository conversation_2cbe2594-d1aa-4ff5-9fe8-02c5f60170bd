from __future__ import print_function
import gate_api
from gate_api import ApiClient, Configuration, Order, SpotApi
from gate_api.exceptions import ApiException, GateApiException
import logging
from decimal import Decimal as D

# 设置日志级别为 INFO
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure APIv4 key authorization
configuration = gate_api.Configuration(
    host="https://api.gateio.ws/api/v4",
    key="",
    secret=""
)


def spot_trade(currency_pair):
    api_client = gate_api.ApiClient(configuration)
    spot_api = gate_api.SpotApi(api_client)
    currency = currency_pair.split("_")[1]

    try:
        # https://github.com/gateio/gateapi-python/blob/master/example/spot.py
        # https://www.gate.io/docs/developers/apiv4/#create-an-order

        # pair = spot_api.get_currency_pair(currency_pair)
        # min_amount = pair.min_base_amount if pair.min_base_amount is not None else 1
        # tickers = spot_api.list_tickers(currency_pair=currency_pair)
        # assert len(tickers) == 1
        # last_price = tickers[0].last

        # make sure balance is enough
        # order_amount = D(min_amount) * 2
        accounts = spot_api.list_spot_accounts(currency=currency)
        available = D(accounts[0].available)
        logger.info("Account available: %s %s", str(available), currency)

        # market price buy
        order_amount = 20
        if available < order_amount:
            logger.error("Account balance not enough")
            return

        order = Order(
            amount=str(order_amount),
            type='market',
            side='buy',
            currency_pair=currency_pair,
            time_in_force="ioc"
        )
        logger.info("place a spot %s order in %s with amount %s and price %s", order.side, order.currency_pair,
                    order.amount, order.price)

        created = spot_api.create_order(order)
        logger.info("order created with id %s, status %s", created.id, created.status)
        return created

    except GateApiException as ex:
        print("Gate api exception, label: %s, message: %s\n" % (ex.label, ex.message))
        return None
    except ApiException as e:
        print("Exception when calling AccountApi->get_account_detail: %s\n" % e)
        return None
