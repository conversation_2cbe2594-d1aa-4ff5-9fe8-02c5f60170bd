import requests
import json
import time


def check_state():
    # https://weixin.91160.com/h5/register/doctor/transfer.html?unit_id=111&dep_id=200185203&from_function_id=UNIT_GH&from_function_name=%E5%8C%BB%E9%99%A2-%E6%8C%82%E5%8F%B7&ill_name=&cf=&is_plat_guahao=0&sub_dep_id=&date=2025-06-16
    url = 'https://wxis.91160.com/wxis/doc/main.do?unit_id=111&dep_id=200185203&date=2025-06-16'

    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.41(0x1800292e) NetType/4G Language/zh_CN',
        'Referer': 'https://wxis.91160.com/wxis/doc/main.do'
    }

    response = requests.get(url, headers=headers)

    if "state = '2'" in response.text:
        # notice, webhook, feishu etc.
        print('available.')
    else:
         print('no result, continue.')

    # time.sleep(5)
    # check_state()


check_state()



