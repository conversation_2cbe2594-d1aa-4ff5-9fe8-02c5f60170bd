import requests
import time
import hashlib


# https://extrastu.cn/new 这里有数据源
def _getSign(e, t):
    e = _ksort(e)
    a = ""
    for k, v in e.items():
        if v is not None:
            a += str(k) + "=" + str(v) + "&"
    a = a[:-1]
    return hashlib.md5((a + "dbbc1dd37360b4084c3a69346e0ce2b2." + t).encode('utf-8')).hexdigest()


def _ksort(e):
    return {k: e[k] for k in sorted(e)}


def get_data(slug):
    timestamp = str(int(time.time()))
    sign = _getSign({}, timestamp)
    res = requests.get(f"https://api.xiaobot.net/paper/{slug}", data={}, headers={
        "Accept": "application/json, text/plain, */*",
        "Api-Key": "xiaobot_web",
        "App-Version": "0.1",
        "Authorization": "Bearer 1548165|yavj2CUbf3bDy2jl7TzbtnzzynLLEcW9izgV3hbn",
        "Timestamp": timestamp,
        "Sign": sign,
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    })
    return res.json()


def create_post(slug):

    data = get_data(slug)
    content = f"""---
author: 姬小光
pubDatetime: 2024-03-05
modDatetime: 2024-03-05
title: 小报童精品专栏：{data['data']['name']}
slug: my-favorite-xiao-bao-tong-column-{slug}
featured: false
draft: false
tags:
  - read
  - learn
description:
  知识付费精选 - 小报童精品专栏，收集全网排行靠前的小报童专栏：{data['data']['name']}
---
    
# {data['data']['name']}
## by {data['data']['creator']['nickname']}

{data['data']['intro']}

读者: {data['data']['subscriber_count']}, 内容: {data['data']['post_count']}

详情: [{data['data']['name']}](https://xiaobot.net/p/{slug}?refer=ebddaa9f-0632-46cd-8c57-8a0c1b089f91)

"""

    output_file = f'/Users/<USER>/Documents/idea_projects/hongkong/vercel/astro-paper/src/content/blog/2024/xiao-bao-tong-{slug}.md'
    with open(output_file, 'w') as file:
        file.write(content)
        print(slug, ' finished.')


def batch_create():
    slug_list = [
        'aiyanjiu',
        'pmthinking2023',
        'xhs',
        'aigclogo',
        'aiclass',
        'ove2022',
        'xiaoxi',
        '77prompt',
        'superindividual',
        'chat-gpt',
        'large-scale-sys',
        'gift',
        'remote-work',
        'alewrites',
        'xiaobushous1',
        'xiaohongshuku',
        'pmthinking2022',
        'melow',
        'mj2023',
        'webnotes',
        'pmdogs4',
        'qianguyihao',
        'chat',
        'system-thinking',
        'yoho',
        'shengcaiyoushu',
        'learningmachine',
        'abby',
        'wuzhu',
        'yibentong',
        'kenshin',
        'smalltalk',
        'suiyisouxun2023',
        'yushuzhilan',
        'aigc1000',
        'pm-play-book',
        'card',
        'b23',
        'pmsink',
        'aiworld',
        'pmdogs5',
        'ml100',
        'never',
        'yang',
        'ceo666',
        'hardwaylab',
        'chatgpt365',
        'survey',
        'xiaolin',
        '011218',
        'digital-life',
        'emptycup',
        'mxfreview',
        'aibot101',
        'chatai666',
        'linqingpro',
        'misso',
        'xiubing',
        'xdash',
        'pmdogs',
        'theoldnotes',
        'peter1981_hr',
        'meditation101',
        'superhuang',
        'ideas',
        'brandaffeine',
        'ip100',
        'tb108',
        'chadandrygoods',
        'whereisyi2',
        'pmthinking2020',
        'undefined',
        'xuyuanpu'
    ]
    for slug in slug_list:
        create_post(slug)
        time.sleep(3)


# batch_create()
print(get_data('aiyanjiu'))

