
text_data = """
杨绛：人生没有谁比谁更容易，只有谁比谁更能熬

绿标可收听洞见主播素年锦时朗读音频杨绛先生在《走到人生边上》写道：“在这物欲横流的人世间，人生一世实在是够苦的。”她曾为柴米油盐之事操心，有谋生之累。
洞见
2022-8-5
阅读 10万+

杨绛先生说：“记住，和周围人搞好关系的秘诀就是，不要和他们分享任何成功的喜悦，和任何开心的事儿。”

1杨绛先生说：“记住，和周围人搞好关系的秘诀就是，不要和他们分享任何成功的喜悦，和任何开心的事儿。”这个周围人，指的是工作中的同事、普通关系的朋友和一般的亲戚，他们都是不会真的希望你成功的人。
亦芝认知觉醒之...
29 day(s) ago
阅读 10万+

杨绛说：聪明的女人，宁可守着无爱的婚姻也不会离婚。因为她知道至少原配对孩子是真心的。结过婚的女人，其实和谁过都一样！

杨绛先生说：聪明的女人，宁可守着无爱的婚姻也不会离婚。因为她知道和谁过都一样，和原配在一起，最起码还有一个目标，就是为了孩子。男人是长工，女人是保姆，换了谁都一样，最好别离婚，对付着过吧。
心灵拾遗
10 day(s) ago
阅读 10万+

杨绛说：“最聪明的女人一涉及自己的感情问题就变成笨蛋；最聪明的男人一涉及自己的感情问题就变成哲学家。

她说：“最聪明的女人一涉及自己的感情问题就变成笨蛋；最聪明的男人一涉及自己的感情问题就变成哲学家。”这句话揭示了人们在感情面前常常失去理智，不论是聪明的女人还是聪明的男人，都难以抵挡感情的诱惑和困扰。
大头C
15 day(s) ago
阅读 10万+

杨绛先生说：“女人最大的悲哀，就是一辈子都没弄明白，一个简单的道理，那就是，这个世界，终其一生都是你一个人。

杨绛先生说：“女人最大的悲哀，就是一辈子都没弄明白，一个简单的道理，那就是，这个世界，终其一生都是你一个人。女人结婚以后就没有家了，在娘家是客人，在婆家是外人，只能一个人扛下所有，原来自己才是自己的家。
老孙故事
2 month(s) ago
阅读 10万+

杨绛留给世人的劝诫：一个家庭最大的悲哀，不是贫穷和争吵，而是父母到了五十岁，还是依旧处于这2种状态

杨绛在《走到人生边上》一书里说：“有修养的人，能喜怒不形于色，能管理好自己的情绪。毕竟人只有在冷静的情况下，才能睿智做出判断“。无论任何适合，都要管理好自己的情绪，不要总是把情绪展现在脸上，尤其是将负面情绪带给别人。
为游社
18 day(s) ago
阅读 10万+

杨绛说：“喜欢吼老公，吼孩子的女人，看上去是家里最凶、最厉害、最强势的那个人。但其实，她是最顾家、最操心的那个人！”

杨绛说：“喜欢吼老公，吼孩子的女人，看上去是家里最凶、最厉害、最强势的那个人。但其实，她是最顾家、最操心的那个人。”01杨绛先生的这句话让我想起了我的母亲。
可爱的可乐吖
13 day(s) ago
阅读 10万+

杨绛说：一个男人，他最大的失败就是：把一个当初什么都不图你，只图对她好的女人，逼成了女汉子，疯子，最后绝情的离开！

杨绛说：一个男人，他最大的失败就是：把一个当初什么都不图你，只图对她好的女人，逼成了女汉子，疯子，最后绝情的离开！一个女人，她最大的失败就是：用自己的青春，去陪伴一个没有担当的男人，用自己的离开，去教会一个男人，懂得如何去珍惜另一个女人！
布衣时光
2 month(s) ago
阅读 10万+

杨绛先生说：聪明的女人，宁可守着无爱的婚姻，也不会离婚。因为她知道和谁过都一样，和原配在一起，最起码还有1个目标

杨绛先生说：“聪明的女人，宁可守着无爱的婚姻，也不会离婚。因为她知道和谁过都一样，和原配在一起，最起码还有一个目标，就是为了孩子。男人是长工，女人是保姆，换了谁都一样，最好别离婚，对付着过吧。”
娱月龙门
2 month(s) ago
阅读 10万+

杨绛说：为什么儿媳妇讨厌婆婆，大部分儿媳讨厌公婆的原因是：付出不多，管的挺多；给的不多，算计挺多；能力不大，口气还挺大。

杨绛说：“为什么儿媳妇讨厌婆婆，大部分儿媳讨厌公婆的原因是：付出不多，管的挺多；给的不多，算计挺多；能力不大，口气还挺大。最主要的是还擅长表演，你遇到了吗？”
我是辉叔叔
26 day(s) ago
阅读 10万+

杨绛说：“一个女人的一生，最应该学会的不是挣钱，也不是打扮自己，而是无论遇到多大的人生风雨，都有让自己快乐起来的能力。”

杨绛说：“一个女人的一生，最应该学会的不是挣钱，也不是打扮自己，而是无论遇到多大的人生风雨，都有让自己快乐起来的能力。”在这个瞬息万变的世界中，每个人都经历着各自不同的人生旅程。
浅读慢行
1 month(s) ago
阅读 10万+
"""

paragraphs = text_data.strip().split("阅读 10万+\n")

# 然后，我们把每个段落分割成行，并把每行的内容保存到字典中
result = []
for paragraph in paragraphs:
    lines = paragraph.split("\n")
    print(len(lines))
    if len(lines) == 4:
        result.append({
            "标题": lines[0],
            "描述": lines[1],
            "名称": lines[2],
            "发布时间": lines[3],
            "阅读数": lines[4]
        })

# 最后，result 就是我们要的结果
print(result)

