#!/usr/local/bin/python3
# -*- coding: UTF-8 -*-

import xlrd
import requests
import ssl

data = xlrd.open_workbook(r"/Users/<USER>/Desktop/域名整改列表.xls")
table = data.sheets()[0]
rows = table.nrows
ssl._create_default_https_context = ssl._create_unverified_context
requests.packages.urllib3.disable_warnings()
out_file = open("http_result.txt", "w")

for i in range(rows):
    cell = table.row_values(i)[2]
    if "中国区域" in cell:
        domain = cell.replace("HTTPS传输协议现有应用改造-中国区域-", "")
        try:
            res = requests.get("http://"+domain, stream=True, verify=False, allow_redirects=False, timeout=10)
            line = "http://"+domain + " " + str(res.status_code)+"\n"
            out_file.write(line)
            print(line)
        except Exception as e:
            continue




