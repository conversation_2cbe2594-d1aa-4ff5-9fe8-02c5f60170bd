import socket

# 1 .com -[whois.verisign-grs.com](http://whois.verisign-grs.com/)
# 2 .net -[whois.crsnic.net](http://whois.crsnic.net/)
# 3 .org -[whois.pir.org](http://whois.pir.org/)
# 4 .info -[whois.afilias.info](http://whois.afilias.info/)
# 5 .biz -[whois.biz](http://whois.biz/)
# 6 .us -[whois.nic.us](http://whois.nic.us/)
# 7 .me -[whois.nic.me](http://whois.nic.me/)
# 8 .io -[whois.nic.io](http://whois.nic.io/)
# 9 .in -[whois.registry.in](http://whois.registry.in/)
# 10 .co -[whois.nic.co](http://whois.nic.co/)
# 11 .ai [whois.nic.ai](http://whois.nic.ai/)

def connect_and_send():
    # 创建一个 socket 对象
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

    # 连接到给定的主机和端口
    s.connect(("anycastdns1-cz.nic.ai", 43))

    # 发送数据
    s.sendall(b"xmo.ai\n")

    # 接收响应
    response = s.recv(1024)

    # 打印响应
    print('Received', repr(response))

    # 关闭 socket
    s.close()


# 调用函数
connect_and_send()
