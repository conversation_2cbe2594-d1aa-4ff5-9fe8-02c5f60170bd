<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Domain Name Generator - Create Unique Domain Names Instantly</title>
    <meta name="description" content="Generate unique domain names using multiple dictionaries. Combine words, prefixes, and suffixes to create perfect domain names for your business or project.">
    <meta name="keywords" content="domain name generator, domain names, business names, website names, domain creation tool">
    <meta name="author" content="Domain Name Generator">
    <meta property="og:title" content="Domain Name Generator - Create Unique Domain Names">
    <meta property="og:description" content="Generate unique domain names using multiple dictionaries and customizable parameters.">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Domain Name Generator">
    <meta name="twitter:description" content="Generate unique domain names using multiple dictionaries and customizable parameters.">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#6366f1',
                        'secondary': '#8b5cf6',
                        'accent': '#06b6d4',
                        'neutral': '#64748b',
                        'base': '#f8fafc',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-base text-neutral-800 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-neutral-200">
        <div class="container mx-auto px-4 py-4">
            <h1 class="text-2xl font-bold text-primary">Domain Name Generator</h1>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 container mx-auto px-4 py-8">
        <div class="max-w-7xl mx-auto">
            <!-- Hero Section -->
            <section class="text-center mb-12">
                <h2 class="text-4xl font-bold text-neutral-900 mb-4">Create Perfect Domain Names</h2>
                <p class="text-xl text-neutral-600 max-w-2xl mx-auto">Generate unique domain names using our powerful combination engine. Mix and match from multiple dictionaries to find the perfect name for your project.</p>
            </section>

            <!-- Generator Section -->
            <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
                <!-- Left Panel - Controls -->
                <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-6">
                    <h3 class="text-xl font-semibold mb-6 text-neutral-800">Generator Settings</h3>
                    
                    <form id="domainForm" class="space-y-5">
                        <!-- Prefix -->
                        <div>
                            <label for="starts_with" class="block text-sm font-medium text-neutral-700 mb-2">
                                Prefix <span class="text-neutral-400 text-xs">(optional)</span>
                            </label>
                            <input type="text" id="starts_with" name="starts_with" 
                                   placeholder="e.g., my, super, best" 
                                   class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors">
                        </div>
                        
                        <!-- Dictionary 1 -->
                        <div>
                            <label for="dict1" class="block text-sm font-medium text-neutral-700 mb-2">
                                First Dictionary <span class="text-neutral-400 text-xs">(optional)</span>
                            </label>
                            <select id="dict1" name="dict1" 
                                    class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors">
                                <option value="">Select first dictionary</option>
                            </select>
                        </div>
                        
                        <!-- Separator -->
                        <div>
                            <label for="separator" class="block text-sm font-medium text-neutral-700 mb-2">
                                Separator <span class="text-neutral-400 text-xs">(optional)</span>
                            </label>
                            <input type="text" id="separator" name="separator" 
                                   placeholder="e.g., -, _, ." 
                                   class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors">
                        </div>
                        
                        <!-- Dictionary 2 -->
                        <div>
                            <label for="dict2" class="block text-sm font-medium text-neutral-700 mb-2">
                                Second Dictionary <span class="text-neutral-400 text-xs">(optional)</span>
                            </label>
                            <select id="dict2" name="dict2" 
                                    class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors">
                                <option value="">Select second dictionary</option>
                            </select>
                        </div>
                        
                        <!-- Suffix -->
                        <div>
                            <label for="ends_with" class="block text-sm font-medium text-neutral-700 mb-2">
                                Suffix <span class="text-neutral-400 text-xs">(optional)</span>
                            </label>
                            <input type="text" id="ends_with" name="ends_with" 
                                   placeholder="e.g., .com, app, ly" 
                                   class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors">
                        </div>
                        
                        <!-- Advanced Options -->
                        <div class="border-t border-neutral-200 pt-5">
                            <h4 class="text-sm font-medium text-neutral-700 mb-3">Advanced Options</h4>
                            
                            <!-- Length Range -->
                            <div class="mb-4">
                                <label class="block text-sm font-medium text-neutral-700 mb-2">
                                    Length Range <span class="text-neutral-400 text-xs">(optional)</span>
                                </label>
                                <div class="flex gap-2">
                                    <input type="number" id="min_length" name="min_length" 
                                           placeholder="Min" min="1" max="63" 
                                           class="flex-1 px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors">
                                    <span class="flex items-center px-2 text-neutral-400">to</span>
                                    <input type="number" id="max_length" name="max_length" 
                                           placeholder="Max" min="1" max="63" 
                                           class="flex-1 px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors">
                                </div>
                            </div>
                            
                            <!-- Result Limit -->
                            <div class="mb-4">
                                <label for="limit" class="block text-sm font-medium text-neutral-700 mb-2">
                                    Result Limit <span class="text-neutral-400 text-xs">(default: 1000)</span>
                                </label>
                                <input type="number" id="limit" name="limit" 
                                       placeholder="Maximum results" min="1" max="10000" 
                                       class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors">
                            </div>
                            
                            <!-- Random Order -->
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="random_order" name="random_order" 
                                           class="mr-3 rounded border-neutral-300 text-primary focus:ring-primary/50">
                                    <span class="text-sm font-medium text-neutral-700">Randomize results</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Generate Button -->
                        <button type="submit" 
                                class="w-full bg-primary text-white py-3 px-4 rounded-lg hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 transition-all duration-200 font-medium">
                            Generate Domain Names
                        </button>
                    </form>
                </div>
                
                <!-- Right Panel - Results -->
                <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-6">
                    <h3 class="text-xl font-semibold mb-4 text-neutral-800">Generated Results</h3>
                    
                    <!-- Format Controls -->
                    <div class="flex gap-2 mb-4">
                        <button id="formatNewlineBtn"
                                class="px-4 py-2 text-sm bg-primary text-white border border-primary rounded-lg hover:bg-primary/90 hover:border-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm"
                                disabled>
                            Line Format
                        </button>
                        <button id="formatCommaBtn"
                                class="px-4 py-2 text-sm bg-accent text-white border border-accent rounded-lg hover:bg-accent/90 hover:border-accent/90 focus:outline-none focus:ring-2 focus:ring-accent/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm"
                                disabled>
                            Comma Format
                        </button>
                    </div>
                    
                    <!-- Results Display -->
                    <div class="mb-4">
                        <textarea id="results" rows="16"
                                  class="w-full px-3 py-2 border border-neutral-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors resize-none"
                                  placeholder="Generated domain names will appear here..."></textarea>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex gap-2 mb-4">
                        <button id="copyBtn"
                                class="flex-1 bg-accent text-white py-2 px-3 rounded-lg hover:bg-accent/90 focus:outline-none focus:ring-2 focus:ring-accent/50 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled>
                            Copy
                        </button>
                        <button id="exportBtn"
                                class="flex-1 bg-primary text-white py-2 px-3 rounded-lg hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled>
                            Export CSV
                        </button>
                    </div>

                    <!-- Availability Check Configuration -->
                    <div class="border-t border-neutral-200 pt-4">
                        <h4 class="text-sm font-medium text-neutral-700 mb-3">Check Availability</h4>

                        <!-- URL Pattern Input -->
                        <div class="mb-3">
                            <label for="defaultPattern" class="block text-xs font-medium text-neutral-600 mb-1">
                                URL Pattern <span class="text-neutral-400">(use {result} as placeholder)</span>
                            </label>
                            <div class="flex gap-2">
                                <input type="text" id="defaultPattern"
                                       value="https://www.name.com/names/{result}"
                                       placeholder="https://example.com/search?q={result}"
                                       class="flex-1 px-2 py-1 text-sm border border-neutral-300 rounded focus:outline-none focus:ring-1 focus:ring-primary/50 focus:border-primary transition-colors">
                                <button id="checkAvailabilityBtn" type="button"
                                        class="px-4 py-1 text-sm bg-secondary text-white rounded hover:bg-secondary/90 focus:outline-none focus:ring-2 focus:ring-secondary/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                        disabled>
                                    Check Availability
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status -->
                    <div id="status" class="mt-4 text-sm text-neutral-600"></div>
                </div>
            </section>

            <!-- How to Use Section -->
            <section class="mb-16">
                <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-8">
                    <h3 class="text-2xl font-bold text-neutral-900 mb-6">How to Use the Domain Name Generator</h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="text-primary font-bold text-lg">1</span>
                            </div>
                            <h4 class="font-semibold text-neutral-800 mb-2">Choose Your Parameters</h4>
                            <p class="text-sm text-neutral-600">Select prefixes, dictionaries, separators, and suffixes to customize your domain generation.</p>
                        </div>

                        <div class="text-center">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="text-primary font-bold text-lg">2</span>
                            </div>
                            <h4 class="font-semibold text-neutral-800 mb-2">Set Filters</h4>
                            <p class="text-sm text-neutral-600">Configure length ranges, result limits, and enable random ordering for varied results.</p>
                        </div>

                        <div class="text-center">
                            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                                <span class="text-primary font-bold text-lg">3</span>
                            </div>
                            <h4 class="font-semibold text-neutral-800 mb-2">Generate & Export</h4>
                            <p class="text-sm text-neutral-600">Generate domain names, copy results, check availability, or export to CSV for further analysis.</p>
                        </div>
                    </div>

                    <div class="bg-neutral-50 rounded-lg p-6">
                        <h4 class="font-semibold text-neutral-800 mb-3">Pro Tips:</h4>
                        <ul class="space-y-2 text-sm text-neutral-600">
                            <li class="flex items-start">
                                <span class="text-primary mr-2">•</span>
                                <span>All parameters are optional - you can use any combination that suits your needs</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-primary mr-2">•</span>
                                <span>Separators are only used when both dictionaries are selected</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-primary mr-2">•</span>
                                <span>Use length filters to ensure domain names fit your requirements</span>
                            </li>
                            <li class="flex items-start">
                                <span class="text-primary mr-2">•</span>
                                <span>Enable random ordering to discover unexpected combinations</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Dictionary Reference Section -->
            <section class="mb-16">
                <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-8">
                    <h3 class="text-2xl font-bold text-neutral-900 mb-6">Available Dictionaries</h3>
                    <p class="text-neutral-600 mb-8">Choose from our curated collection of word dictionaries to create the perfect domain name combinations.</p>

                    <div id="dictionaryReference" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Dictionary cards will be populated by JavaScript -->
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-neutral-200 mt-auto">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center text-sm text-neutral-600">
                <p>&copy; 2024 Domain Name Generator. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="app.js"></script>
</body>
</html>
