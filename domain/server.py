from fastapi import FastAPI, Query
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Optional
import uvicorn
import os
import random

app = FastAPI(title="Domain Search API", description="API for generating domain name combinations", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dictionary definitions copied from domain_search.py
number = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
pinyin = ["a", "ai", "an", "ang", "ao", "ba", "bai", "ban", "bang", "bao", "bei", "ben", "beng", "bi", "bian", "biao",
          "bie", "bin", "bing", "bo", "bu", "ca", "cai", "can", "cang", "cao", "ce", "cen", "ceng", "cha", "chai",
          "chan", "chang", "chao", "che", "chen", "cheng", "chi", "chong", "chou", "chu", "chuai", "chuan", "chuang",
          "chui", "chun", "chuo", "ci", "cong", "cou", "cu", "cuan", "cui", "cun", "cuo", "da", "dai", "dan", "dang",
          "dao", "de", "dei", "deng", "di", "dia", "dian", "diao", "die", "ding", "diu", "dong", "dou", "du", "duan",
          "dui", "dun", "duo", "e", "ei", "en", "er", "fa", "fan", "fang", "fei", "fen", "feng", "fo", "fou", "fu",
          "ga", "gai", "gan", "gang", "gao", "ge", "gei", "gen", "geng", "gong", "gou", "gu", "gua", "guai", "guan",
          "guang", "gui", "gun", "guo", "ha", "hai", "han", "hang", "hao", "he", "hei", "hen", "heng", "hong", "hou",
          "hu", "hua", "huai", "huan", "huang", "hui", "hun", "huo", "ji", "jia", "jian", "jiang", "jiao", "jie", "jin",
          "jing", "jiong", "jiu", "ju", "juan", "jue", "jun", "ka", "kai", "kan", "kang", "kao", "ke", "ken", "keng",
          "kong", "kou", "ku", "kua", "kuai", "kuan", "kuang", "kui", "kun", "kuo", "la", "lai", "lan", "lang", "lao",
          "le", "lei", "leng", "li", "lian", "liang", "liao", "lie", "lin", "ling", "liu", "long", "lou", "lu", "luan",
          "lun", "luo", "lv", "lve", "lue", "ma", "mai", "man", "mang", "mao", "me", "mei", "men", "meng", "mi", "mian",
          "miao", "mie", "min", "ming", "miu", "mo", "mou", "mu", "na", "nai", "nan", "nang", "nao", "ne", "nei", "nen",
          "neng", "ni", "nian", "niang", "niao", "nie", "nin", "ning", "niu", "nong", "nou", "nu", "nuan", "nuo", "nv",
          "nve", "nue", "o", "ou", "pa", "pai", "pan", "pang", "pao", "pei", "pen", "peng", "pi", "pian", "piao", "pie",
          "pin", "ping", "po", "pou", "pu", "qi", "qia", "qian", "qiang", "qiao", "qie", "qin", "qing", "qiong", "qiu",
          "qu", "quan", "que", "qun", "ran", "rang", "rao", "re", "ren", "reng", "ri", "rong", "rou", "ru", "ruan",
          "rui", "run", "ruo", "sa", "sai", "san", "sang", "sao", "se", "sen", "seng", "sha", "shai", "shan", "shang",
          "shao", "she", "shen", "sheng", "shi", "shou", "shu", "shua", "shuai", "shuan", "shuang", "shui", "shun",
          "shuo", "si", "song", "sou", "su", "suan", "sui", "sun", "suo", "ta", "tai", "tan", "tang", "tao", "te",
          "teng", "ti", "tian", "tiao", "tie", "ting", "tong", "tou", "tu", "tuan", "tui", "tun", "tuo", "wa", "wai",
          "wan", "wang", "wei", "wen", "weng", "wo", "wu", "xi", "xia", "xian", "xiang", "xiao", "xie", "xin", "xing",
          "xiong", "xiu", "xu", "xuan", "xue", "xun", "ya", "yan", "yang", "yao", "ye", "yi", "yin", "ying", "yong",
          "you", "yu", "yuan", "yue", "yun", "za", "zai", "zan", "zang", "zao", "ze", "zei", "zen", "zeng", "zha",
          "zhai", "zhan", "zhang", "zhao", "zhe", "zhen", "zheng", "zhi", "zhong", "zhou", "zhu", "zhua", "zhuai",
          "zhuan", "zhuang", "zhui", "zhun", "zhuo", "zi", "zong", "zou", "zu", "zuan", "zui", "zun", "zuo"]

pinyin2 = ["ai", "an", "ao", "ba", "bi", "bo", "bu", "ca", "ce", "ci", "cu", "da", "de", "di", "du", "ei", "en", "er",
           "fa", "fo", "fu", "ga", "ge", "gu", "ha", "he", "hu", "ji", "ju", "ka", "ke", "ku", "la", "le", "li", "lu",
           "lv", "ma", "me", "mi", "mo", "mu", "na", "ne", "ni", "nu", "nv", "ou", "pa", "pi", "po", "pu", "qi", "qu",
           "re", "ri", "ru", "sa", "se", "si", "su", "ta", "te", "ti", "tu", "wa", "wo", "wu", "xi", "xu", "ya", "ye",
           "yi", "yu", "za", "ze", "zi", "zu"]

pinyin3 = ["ang", "bai", "ban", "bao", "bei", "ben", "bie", "bin", "cai", "can", "cao", "cen", "cha", "che", "chi",
           "chu", "cou", "cui", "cun", "cuo", "dai", "dan", "dao", "dei", "dia", "die", "diu", "dou", "dui", "dun",
           "duo", "fan", "fei", "fen", "fou", "gai", "gan", "gao", "gei", "gen", "gou", "gua", "gui", "gun", "guo",
           "hai", "han", "hao", "hei", "hen", "hou", "hua", "hui", "hun", "huo", "jia", "jie", "jin", "jiu", "jue",
           "jun", "kai", "kan", "kao", "ken", "kou", "kua", "kui", "kun", "kuo", "lai", "lan", "lao", "lei", "lie",
           "lin", "liu", "lou", "lun", "luo", "lve", "lue", "mai", "man", "mao", "mei", "men", "mie", "min", "miu",
           "mou", "nai", "nan", "nao", "nei", "nen", "nie", "nin", "niu", "nou", "nuo", "nve", "nue", "pai", "pan",
           "pao", "pei", "pen", "pie", "pin", "pou", "qia", "qie", "qin", "qiu", "que", "qun", "ran", "rao", "ren",
           "rou", "rui", "run", "ruo", "sai", "san", "sao", "sen", "sha", "she", "shi", "shu", "sou", "sui", "sun",
           "suo", "tai", "tan", "tao", "tie", "tou", "tui", "tun", "tuo", "wai", "wan", "wei", "wen", "xia", "xie",
           "xin", "xiu", "xue", "xun", "yan", "yao", "yin", "you", "yue", "yun", "zai", "zan", "zao", "zei", "zen",
           "zha", "zhe", "zhi", "zhu", "zou", "zui", "zun", "zuo"]

pinyin4 = ["bang", "beng", "bian", "biao", "bing", "cang", "ceng", "chai", "chan", "chao", "chen", "chou", "chui",
           "chun", "chuo", "cong", "cuan", "dang", "deng", "dian", "diao", "ding", "dong", "duan", "fang", "feng",
           "gang", "geng", "gong", "guai", "guan", "hang", "heng", "hong", "huai", "huan", "jian", "jiao", "jing",
           "juan", "kang", "keng", "kong", "kuai", "kuan", "lang", "leng", "lian", "liao", "ling", "long", "luan",
           "mang", "meng", "mian", "miao", "ming", "nang", "neng", "nian", "niao", "ning", "nong", "nuan", "pang",
           "peng", "pian", "piao", "ping", "qian", "qiao", "qing", "quan", "rang", "reng", "rong", "ruan", "sang",
           "seng", "shai", "shan", "shao", "shen", "shou", "shua", "shui", "shun", "shuo", "song", "suan", "tang",
           "teng", "tian", "tiao", "ting", "tong", "tuan", "wang", "weng", "xian", "xiao", "xing", "xuan", "yang",
           "ying", "yong", "yuan", "zang", "zeng", "zhai", "zhan", "zhao", "zhen", "zhou", "zhua", "zhui", "zhun",
           "zhuo", "zong", "zuan"]

abc = ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v",
       "w", "x", "y", "z"]

abc2 = ["aa", "ab", "ac", "ad", "ae", "af", "ag", "ah", "ai", "aj", "ak", "al", "am", "an", "ao", "ap", "aq", "ar",
        "as", "at", "au", "av", "aw", "ax", "ay", "az", "ba", "bb", "bc", "bd", "be", "bf", "bg", "bh", "bi", "bj",
        "bk", "bl", "bm", "bn", "bo", "bp", "bq", "br", "bs", "bt", "bu", "bv", "bw", "bx", "by", "bz", "ca", "cb",
        "cc", "cd", "ce", "cf", "cg", "ch", "ci", "cj", "ck", "cl", "cm", "cn", "co", "cp", "cq", "cr", "cs", "ct",
        "cu", "cv", "cw", "cx", "cy", "cz", "da", "db", "dc", "dd", "de", "df", "dg", "dh", "di", "dj", "dk", "dl",
        "dm", "dn", "do", "dp", "dq", "dr", "ds", "dt", "du", "dv", "dw", "dx", "dy", "dz", "ea", "eb", "ec", "ed",
        "ee", "ef", "eg", "eh", "ei", "ej", "ek", "el", "em", "en", "eo", "ep", "eq", "er", "es", "et", "eu", "ev",
        "ew", "ex", "ey", "ez", "fa", "fb", "fc", "fd", "fe", "ff", "fg", "fh", "fi", "fj", "fk", "fl", "fm", "fn",
        "fo", "fp", "fq", "fr", "fs", "ft", "fu", "fv", "fw", "fx", "fy", "fz", "ga", "gb", "gc", "gd", "ge", "gf",
        "gg", "gh", "gi", "gj", "gk", "gl", "gm", "gn", "go", "gp", "gq", "gr", "gs", "gt", "gu", "gv", "gw", "gx",
        "gy", "gz", "ha", "hb", "hc", "hd", "he", "hf", "hg", "hh", "hi", "hj", "hk", "hl", "hm", "hn", "ho", "hp",
        "hq", "hr", "hs", "ht", "hu", "hv", "hw", "hx", "hy", "hz", "ia", "ib", "ic", "id", "ie", "if", "ig", "ih",
        "ii", "ij", "ik", "il", "im", "in", "io", "ip", "iq", "ir", "is", "it", "iu", "iv", "iw", "ix", "iy", "iz",
        "ja", "jb", "jc", "jd", "je", "jf", "jg", "jh", "ji", "jj", "jk", "jl", "jm", "jn", "jo", "jp", "jq", "jr",
        "js", "jt", "ju", "jv", "jw", "jx", "jy", "jz", "ka", "kb", "kc", "kd", "ke", "kf", "kg", "kh", "ki", "kj",
        "kk", "kl", "km", "kn", "ko", "kp", "kq", "kr", "ks", "kt", "ku", "kv", "kw", "kx", "ky", "kz", "la", "lb",
        "lc", "ld", "le", "lf", "lg", "lh", "li", "lj", "lk", "ll", "lm", "ln", "lo", "lp", "lq", "lr", "ls", "lt",
        "lu", "lv", "lw", "lx", "ly", "lz", "ma", "mb", "mc", "md", "me", "mf", "mg", "mh", "mi", "mj", "mk", "ml",
        "mm", "mn", "mo", "mp", "mq", "mr", "ms", "mt", "mu", "mv", "mw", "mx", "my", "mz", "na", "nb", "nc", "nd",
        "ne", "nf", "ng", "nh", "ni", "nj", "nk", "nl", "nm", "nn", "no", "np", "nq", "nr", "ns", "nt", "nu", "nv",
        "nw", "nx", "ny", "nz", "oa", "ob", "oc", "od", "oe", "of", "og", "oh", "oi", "oj", "ok", "ol", "om", "on",
        "oo", "op", "oq", "or", "os", "ot", "ou", "ov", "ow", "ox", "oy", "oz", "pa", "pb", "pc", "pd", "pe", "pf",
        "pg", "ph", "pi", "pj", "pk", "pl", "pm", "pn", "po", "pp", "pq", "pr", "ps", "pt", "pu", "pv", "pw", "px",
        "py", "pz", "qa", "qb", "qc", "qd", "qe", "qf", "qg", "qh", "qi", "qj", "qk", "ql", "qm", "qn", "qo", "qp",
        "qq", "qr", "qs", "qt", "qu", "qv", "qw", "qx", "qy", "qz", "ra", "rb", "rc", "rd", "re", "rf", "rg", "rh",
        "ri", "rj", "rk", "rl", "rm", "rn", "ro", "rp", "rq", "rr", "rs", "rt", "ru", "rv", "rw", "rx", "ry", "rz",
        "sa", "sb", "sc", "sd", "se", "sf", "sg", "sh", "si", "sj", "sk", "sl", "sm", "sn", "so", "sp", "sq", "sr",
        "ss", "st", "su", "sv", "sw", "sx", "sy", "sz", "ta", "tb", "tc", "td", "te", "tf", "tg", "th", "ti", "tj",
        "tk", "tl", "tm", "tn", "to", "tp", "tq", "tr", "ts", "tt", "tu", "tv", "tw", "tx", "ty", "tz", "ua", "ub",
        "uc", "ud", "ue", "uf", "ug", "uh", "ui", "uj", "uk", "ul", "um", "un", "uo", "up", "uq", "ur", "us", "ut",
        "uu", "uv", "uw", "ux", "uy", "uz", "va", "vb", "vc", "vd", "ve", "vf", "vg", "vh", "vi", "vj", "vk", "vl",
        "vm", "vn", "vo", "vp", "vq", "vr", "vs", "vt", "vu", "vv", "vw", "vx", "vy", "vz", "wa", "wb", "wc", "wd",
        "we", "wf", "wg", "wh", "wi", "wj", "wk", "wl", "wm", "wn", "wo", "wp", "wq", "wr", "ws", "wt", "wu", "wv",
        "ww", "wx", "wy", "wz", "xa", "xb", "xc", "xd", "xe", "xf", "xg", "xh", "xi", "xj", "xk", "xl", "xm", "xn",
        "xo", "xp", "xq", "xr", "xs", "xt", "xu", "xv", "xw", "xx", "xy", "xz", "ya", "yb", "yc", "yd", "ye", "yf",
        "yg", "yh", "yi", "yj", "yk", "yl", "ym", "yn", "yo", "yp", "yq", "yr", "ys", "yt", "yu", "yv", "yw", "yx",
        "yy", "yz", "za", "zb", "zc", "zd", "ze", "zf", "zg", "zh", "zi", "zj", "zk", "zl", "zm", "zn", "zo", "zp",
        "zq", "zr", "zs", "zt", "zu", "zv", "zw", "zx", "zy", "zz"]

words = ["a", "ability", "able", "about", "above", "accept", "accord", "account", "across", "act", "action", "active",
         "actual", "add", "address", "admit", "advance", "advantage", "affair", "after", "afternoon", "again",
         "against", "age", "agency", "ago", "ahead", "air", "all", "allow", "almost", "alone", "along", "already",
         "also", "although", "always", "among", "amount", "and", "animal", "another", "answer", "any", "anyone",
         "anything", "appear", "apply", "approve", "argue", "arm", "army", "around", "arrange", "arrive", "art",
         "article", "as", "ask", "association", "at", "attack", "attempt", "attend", "attention", "audience", "average",
         "avoid", "away", "bad", "balance", "ball", "bank", "bar", "base", "basic", "basis", "battle", "be", "bear",
         "beat", "beauty", "because", "become", "bed", "before", "begin", "behavior", "behind", "believe", "below",
         "best", "better", "between", "beyond", "big", "bill", "bit", "black", "block", "blood", "blue", "board",
         "boat", "body", "book", "both", "bottle", "bottom", "boy", "break", "bridge", "bright", "bring", "broad",
         "brother", "build", "burn", "business", "but", "buy", "by", "call", "camp", "can", "capital", "captain", "car",
         "care", "carry", "case", "catch", "cattle", "cause", "cent", "center", "century", "certain", "chance",
         "change", "character", "charge", "check", "chief", "child", "choice", "choose", "church", "circle", "citizen",
         "city", "claim", "class", "clean", "clear", "close", "clothe", "club", "coat", "cold", "college", "color",
         "combine", "come", "comfort", "command", "committee", "common", "company", "compare", "complete", "compose",
         "concern", "condition", "conscious", "consider", "contain", "continue", "control", "cool", "corner", "cost",
         "could", "council", "count", "country", "course", "court", "cover", "critic", "cross", "crowd", "cry",
         "current", "cut", "daily", "dance", "danger", "dark", "date", "day", "dead", "deal", "death", "decide",
         "decision", "declare", "deep", "defense", "degree", "demand", "department", "depend", "dependent", "describe",
         "desire", "destroy", "detail", "determine", "develop", "die", "difference", "different", "difficult",
         "difficulty", "dinner", "direct", "direction", "director", "discover", "discuss", "discussion", "distance",
         "district", "division", "do", "doctor", "dog", "dollar", "door", "doubt", "down", "draw", "dream", "dress",
         "drink", "drive", "drop", "dry", "due", "during", "dust", "duty", "each", "early", "earth", "east", "easy",
         "eat", "edge", "education", "effect", "effective", "effort", "either", "election", "electric", "else",
         "employ", "employee", "encourage", "end", "enemy", "engineer", "English", "enjoy", "enough", "enter", "entire",
         "equal", "escape", "especially", "essential", "even", "evening", "event", "ever", "every", "everyone",
         "everything", "exact", "examine", "example", "except", "excite", "exercise", "exist", "existence", "expect",
         "expense", "experience", "experiment", "explain", "express", "extend", "extent", "extreme", "eye", "face",
         "fact", "fail", "fair", "faith", "fall", "fame", "familiar", "family", "far", "farm", "fast", "father",
         "favor", "fear", "feed", "feel", "few", "field", "fight", "figure", "fill", "film", "find", "fine", "finger",
         "finish", "fire", "firm", "first", "fit", "fix", "floor", "flow", "fly", "follow", "food", "foot", "for",
         "force", "foreign", "forget", "form", "former", "forward", "frame", "free", "freedom", "frequent", "friend",
         "from", "front", "full", "further", "future", "gain", "game", "garden", "gas", "general", "get", "girl",
         "give", "glass", "go", "god", "good", "govern", "governor", "great", "green", "ground", "group", "grow",
         "growth", "guest", "gun", "hair", "half", "hall", "hand", "handle", "hang", "happen", "happy", "hard",
         "hardly", "have", "he", "head", "health", "hear", "heart", "heat", "heavy", "help", "here", "high", "hill",
         "history", "hit", "hold", "hole", "home", "honor", "hope", "horse", "hospital", "hot", "hotel", "hour",
         "house", "how", "however", "human", "husband", "I", "idea", "ideal", "if", "imagine", "immediate",
         "importance", "important", "improve", "in", "inch", "include", "increase", "indeed", "industry", "influence",
         "inform", "inside", "instead", "interest", "international", "into", "island", "it", "join", "judge", "just",
         "justice", "keep", "kind", "king", "kitchen", "know", "knowledge", "lack", "lady", "land", "language",
         "large", "last", "late", "latter", "laugh", "law", "lay", "lead", "learn", "least", "leave", "left", "leg",
         "length", "less", "let", "letter", "level", "lie", "life", "light", "like", "likely", "limit", "line", "list",
         "listen", "literature", "little", "live", "load", "local", "lock", "long", "look", "lose", "loss", "lot",
         "love", "low", "machine", "main", "make", "man", "manage", "manner", "manufacture", "many", "mark", "market",
         "marriage", "marry", "mass", "master", "match", "material", "matter", "may", "maybe", "mean", "measure",
         "medical", "meet", "member", "memory", "mention", "mere", "middle", "might", "mile", "mind", "minute", "miss",
         "model", "modern", "moment", "money", "month", "moral", "more", "morning", "most", "mother", "motor",
         "mountain", "mouth", "move", "much", "murder", "music", "must", "name", "nation", "nature", "near",
         "necessary", "need", "neither", "never", "new", "news", "newspaper", "next", "night", "no", "none", "nor",
         "north", "not", "note", "nothing", "notice", "now", "number", "object", "observe", "occasion", "of", "off",
         "offer", "office", "officer", "official", "often", "oil", "old", "on", "once", "one", "only", "open",
         "operate", "operation", "opinion", "opportunity", "or", "order", "organize", "origin", "other", "out",
         "outside", "over", "own", "page", "pain", "paint", "paper", "parent", "park", "part", "particular", "party",
         "pass", "past", "patient", "pattern", "pay", "peace", "people", "per", "perfect", "perform", "performance",
         "perhaps", "permit", "person", "pick", "picture", "piece", "place", "plan", "plant", "play", "please", "poem",
         "poet", "point", "police", "political", "pool", "poor", "popular", "population", "position", "possible",
         "post", "pound", "power", "practical", "practice", "prepare", "present", "president", "press", "pressure",
         "pretty", "prevent", "price", "private", "probable", "problem", "produce", "product", "production",
         "profession", "program", "progress", "promise", "proper", "property", "propose", "prove", "provide", "public",
         "pull", "pure", "purpose", "push", "put", "quality", "question", "quick", "quiet", "quite", "race", "radio",
         "raise", "rapid", "rate", "rather", "reach", "read", "ready", "real", "realize", "reason", "reasonable",
         "receive", "recent", "recognize", "recommend", "record", "red", "reduce", "refer", "reflect", "refuse",
         "regard", "regular", "relate", "relation", "relative", "religion", "remain", "remark", "remember", "repeat",
         "replace", "reply", "report", "represent", "representative", "respect", "responsible", "rest", "result",
         "return", "ride", "right", "rise", "river", "road", "rock", "roll", "room", "round", "rule", "run", "sale",
         "same", "sample", "save", "say", "scene", "school", "science", "sea", "search", "season", "seat", "second",
         "secret", "secretary", "see", "seem", "sell", "send", "sense", "separate", "serious", "serve", "service",
         "set", "settle", "several", "shake", "shall", "shape", "share", "sharp", "she", "shelter", "ship", "shoot",
         "shop", "short", "should", "shoulder", "show", "side", "sight", "sign", "simple", "since", "sing", "single",
         "sit", "situation", "size", "skill", "sleep", "slight", "slow", "small", "smile", "so", "social", "society",
         "soft", "soldier", "solid", "some", "someone", "something", "sometimes", "son", "song", "soon", "sort",
         "sound", "south", "space", "speak", "special", "speed", "spend", "spirit", "spot", "spread", "spring",
         "square", "staff", "stage", "stand", "standard", "start", "state", "station", "stay", "step", "stick", "still",
         "stock", "stop", "store", "story", "straight", "strange", "street", "strength", "strike", "strong", "student",
         "study", "subject", "success", "such", "sudden", "suffer", "suggest", "suit", "summer", "supply", "support",
         "suppose", "sure", "surface", "surprise", "system", "table", "take", "talk", "tax", "teach", "tear",
         "telephone", "tell", "temperature", "tend", "term", "test", "than", "that", "the", "then", "there",
         "therefore", "these", "they", "thick", "thin", "thing", "think", "this", "those", "though", "through", "throw",
         "thus", "time", "tire", "title", "to", "today", "together", "too", "tooth", "top", "total", "touch", "toward",
         "town", "trade", "train", "travel", "treat", "tree", "trial", "trip", "trouble", "true", "truth", "try",
         "turn", "type", "under", "understand", "union", "unit", "unite", "university", "unless", "until", "up", "upon",
         "use", "usual", "value", "various", "very", "view", "visit", "vote", "wage", "wait", "walk", "wall", "want",
         "war", "warm", "wash", "watch", "water", "wave", "way", "we", "weak", "weapon", "wear", "week", "weight",
         "well", "west", "western", "what", "whatever", "when", "where", "whether", "which", "while", "white", "who",
         "whole", "why", "wide", "wife", "will", "win", "wind", "window", "wine", "wish", "with", "within", "without",
         "woman", "wonder", "word", "work", "world", "worry", "worth", "would", "write", "wrong", "yard", "year", "yes",
         "yet", "you", "young", "youth"]

words3 = ['see', 'dog', 'cat', 'sky', 'car', 'run', 'sun', 'bat', 'fox', 'bee', 'zen', 'cut', 'fix', 'tea', 'bug', 'ant', 'mix', 'max', 'joy', 'gum', 'mud', 'app', 'doc', 'com', 'row', 'bot', 'css', 'tie', 'hat', 'box', 'lip', 'net', 'ice', 'ink', 'kid', 'nut', 'rib', 'saw', 'vet', 'web', 'zip', 'aim', 'bit', 'cap', 'dig', 'fit', 'hop', 'jet', 'key', 'log', 'map', 'nap', 'red', 'sip', 'top', 'use', 'van', 'win', 'yet', 'zoo', 'api', 'ram', 'seo', 'gif', 'pdf', 'ppt', 'cms', 'sem', 'crm', 'iot', 'btc', 'eth', 'mac', 'get', 'git', 'all', 'the', 'hot', 'uni', 'hex', 'eco', 'out', 'pro', 'tag', 'pin', 'tap', 'ing', 'ify', 'ize']

words4 = ['base', 'supa', 'ship', 'mall', 'text', 'hair', 'last', 'girl', 'busy', 'fine', 'ring', 'four', 'game', 'face', 'kick', 'love', 'bend', 'fell', 'iron', 'come', 'roll', 'crow', 'move', 'city', 'cash', 'gold', 'fact', 'each', 'calm', 'like', 'open', 'even', 'mild', 'pain', 'have', 'call', 'card', 'fire', 'jump', 'wild', 'mind', 'fair', 'rain', 'bird', 'fish', 'rule', 'sing', 'past', 'back', 'baby', 'bank', 'poll', 'code', 'nose', 'node', 'toll', 'rust', 'pull', 'life', 'into', 'only', 'ever', 'five', 'dark', 'feel', 'dust', 'cook', 'here', 'sale', 'more', 'main', 'sell', 'lady', 'near', 'bell', 'cool', 'fall', 'meet', 'fast', 'evil', 'hear', 'tall', 'chat', 'push', 'kill', 'able', 'also', 'army', 'away', 'ball', 'bear', 'beat', 'best', 'bill', 'blue', 'boat', 'body', 'book', 'both', 'burn', 'camp', 'care', 'case', 'cent', 'club', 'coat', 'cold', 'cost', 'date', 'dead', 'deal', 'deep', 'door', 'down', 'draw', 'drop', 'duty', 'east', 'easy', 'edge', 'else', 'fail', 'fame', 'farm', 'fear', 'feed', 'fill', 'film', 'find', 'firm', 'flow', 'food', 'foot', 'form', 'free', 'from', 'full', 'gain', 'give', 'good', 'grow', 'half', 'hall', 'hand', 'hang', 'hard', 'head', 'heat', 'help', 'high', 'hill', 'hold', 'hole', 'home', 'hope', 'hour', 'idea', 'inch', 'join', 'just', 'keep', 'kind', 'king', 'know', 'lack', 'land', 'late', 'lead', 'left', 'less', 'line', 'list', 'live', 'load', 'lock', 'long', 'look', 'lose', 'loss', 'make', 'many', 'mark', 'mass', 'mean', 'mere', 'mile', 'miss', 'most', 'much', 'must', 'name', 'need', 'news', 'next', 'none', 'note', 'once', 'over', 'page', 'park', 'part', 'pass', 'pick', 'plan', 'play', 'poem', 'poet', 'pool', 'poor', 'post', 'pure', 'race', 'rate', 'read', 'real', 'rest', 'ride', 'rise', 'road', 'rock', 'room', 'same', 'save', 'seat', 'seem', 'send', 'shop', 'show', 'side', 'sign', 'size', 'slow', 'soft', 'some', 'song', 'soon', 'sort', 'spot', 'stay', 'step', 'stop', 'such', 'suit', 'sure', 'take', 'talk', 'tear', 'tell', 'tend', 'term', 'test', 'than', 'that', 'then', 'they', 'thin', 'this', 'thus', 'time', 'tire', 'town', 'tree', 'trip', 'true', 'turn', 'type', 'unit', 'upon', 'very', 'view', 'vote', 'wage', 'wait', 'walk', 'wall', 'want', 'warm', 'wash', 'wave', 'weak', 'wear', 'week', 'well', 'west', 'what', 'when', 'wide', 'wife', 'will', 'wind', 'wine', 'wish', 'with', 'word', 'work', 'yard', 'year']

abc_fav = [
    'ai', 'an', 'im', 'in', 'io', 'mi', 'mo', 'ni', 'ui', 'it', 'to', 'do', 'no',
    'or', 'co', 'xo', 'ux', 'de', 'di', 're', 'kk',
    'we', 'wu', 'ca', 'so', 'su', 'la', 'ok', 'ro', 'hi'
]

coin = ['meta', 'coin', 'bit', 'btc', 'eth', 'crypto', 'chain', 'wallet', 'token']
animals = ["bee", "cat", "fox", "dog", "duck", "bear", "fish", "lion"]
file_extensions = [
    "binary", "json", "xml", "png", "jpg", "html", "js", "css", "yaml", "txt", "text", "csv", "table", "xlsx", "doc", "ppt", "pdf", "zip", "mp3", "wav", "webp", "svg", "ico", "icon"
]

office = ['icon', 'slide', 'voice', 'logo', 'code', 'gif', 'pdf', 'word', 'text', 'mind', 'image', 'photo', 'video', 'table', 'word', 'excel', 'ppt', 'chart', 'doc', 'note']
tech = ['ai', 'app', 'cloud', 'open', 'cms', 'seo', 'sem', 'crm', 'com', 'net', 'tech', 'code', 'node']
fav_full = ['ai', 'app', 'cloud', 'open', 'cms', 'seo', 'sem', 'crm', 'com', 'net', 'tech', 'iot', 'code', 'node', 'meta', 'coin', 'bit', 'btc', 'eth', 'crypto', 'chain', 'wallet', 'token', "bee", "cat", "fox", "dog", "duck", "bear", "fish", "lion", 'word', 'excel', 'ppt', 'pdf', 'mind', 'chart', 'doc', 'note', 'mac', 'flow', 'time', 'todo']

action_after = [
    "translator", "processor", "designer", "compiler", "analyzer", "evaluator", "validator", "sender", "receiver", "interpreter", "uploader", "calculator", "generator", "sample", "template", "formatter", "minifier", "minify", "builder", "schema", "pattern", "checker", "detector", "scraper", "manager", "example", "explorer", "dashboard", "planner", "tracker", "recorder", "optimizer", "scheduler", "converter", "viewer", "extractor", "convert", "monitor", "notifier", "verifier", "simulator", "assistant", "constructor", "comparator", "navigator", "syncer", "connector", "online", "cataloger", "responder", "downloader", "maker", "creator", "editor", "taker", "cleaner"
]

action_before = [
    "convert", "edit", "make", "build", "create", "delete", "update", "read", "write", "execute", "compile", "analyze", "evaluate", "optimize", "track", "plan", "record", "upload", "download", "sync", "monitor", "notify", "verify", "simulate", "compare", "navigate", "connect", "respond", "extract", "generate", "calculate", "format", "design", "manage", "explore", "check", "detect", "scrape", "schedule", "view", "assist", "construct", "organize", "guide", "build", "render", "filter", "sort", "merge", "split", "transform", "store", "retrieve", "search", "replace", "validate", "publish", "share"
]

prefix = ['get', 'git', 'solo', 'talk', 'flow', 're', 'all', 'we', 'the', 'me', 'my', 'mono', 'best', 'smart', 'hot', 'cool', 'fast', 'uni', 'hex', 'eco', 'micro', 'bit', 'x', 'i', 'mini', 'life', 'up', 'on', 'out', 'omni', 'trend', 'tech', 'info', 'nano', 'cyber', 'pro', 'free', 'cut', 'net', 'fix', 'map', 'run', 'mix', 'tag', 'auto', 'zip', 'web', 'pin', 'tap', 'sort', 'scan', 'flip', 'data', 'cloud']

suffix = ['er', 'mall', 'shop', 'ix', 'or', 'ing', 'ify', 'ize', 'ment', 'ware', 'bot', 'cast']

# Dictionary mapping for frontend selection with user-friendly names
DICT_MAPPING = {
    "Numbers (0-9)": number,
    "Chinese Pinyin (Full)": pinyin,
    "Chinese Pinyin (2-Letter)": pinyin2,
    "Chinese Pinyin (3-Letter)": pinyin3,
    "Chinese Pinyin (4-Letter)": pinyin4,
    "English Letters (A-Z)": abc,
    "English Letters (2-Letter)": abc2,
    "Common English Words": words,
    "Short English Words (3-Letter)": words3,
    "Medium English Words (4-Letter)": words4,
    "Favorite Letter Combinations": abc_fav,
    "Cryptocurrency Terms": coin,
    "Animal Names": animals,
    "File Extensions": file_extensions,
    "Office & Productivity": office,
    "Technology Terms": tech,
    "Curated Favorites": fav_full,
    "Action Suffixes": action_after,
    "Action Prefixes": action_before,
    "Common Prefixes": prefix,
    "Common Suffixes": suffix
}

def search_words(dict1: str = "", dict2: str = "", starts_with: str = "", separator: str = "", ends_with: str = "", min_length: int = 1, max_length: int = 63, limit: int = 1000, random_order: bool = False) -> List[str]:
    """
    Generate domain combinations based on two dictionaries and formatting options.

    Args:
        dict1: Name of the first dictionary (optional)
        dict2: Name of the second dictionary (optional)
        starts_with: Prefix to add to each combination
        separator: Separator between dict1 and dict2 words
        ends_with: Suffix to add to each combination
        min_length: Minimum length of generated domains
        max_length: Maximum length of generated domains
        limit: Maximum number of results to return
        random_order: Whether to randomize the order of results

    Returns:
        List of generated domain combinations
    """
    # Handle empty or invalid dictionary selections
    dict_1_words = DICT_MAPPING.get(dict1, [""])
    dict_2_words = DICT_MAPPING.get(dict2, [""])

    full_list = []

    # Follow the original logic: swap dict_1 and dict_2
    dict_1_words, dict_2_words = dict_2_words, dict_1_words

    # If random order is requested, shuffle the dictionaries first
    if random_order:
        dict_1_words = list(dict_1_words)
        dict_2_words = list(dict_2_words)
        random.shuffle(dict_1_words)
        random.shuffle(dict_2_words)

    for word_1 in dict_1_words:
        for word_2 in dict_2_words:
            # Check if we've reached the limit
            if len(full_list) >= limit:
                break

            # Only use separator if both words are non-empty
            if word_1 and word_2:
                domain = starts_with + word_1 + separator + word_2 + ends_with
            else:
                domain = starts_with + word_1 + word_2 + ends_with

            # Check length constraints and ignore empty results
            if domain.strip() and min_length <= len(domain) <= max_length:
                full_list.append(domain)

        # Break outer loop if limit reached
        if len(full_list) >= limit:
            break

    # If random order is requested and we have results, shuffle them again
    if random_order and full_list:
        random.shuffle(full_list)

    return full_list

@app.get("/search", response_model=List[str])
async def search_domains(
    dict1: str = Query("", description="First dictionary name (optional)"),
    dict2: str = Query("", description="Second dictionary name (optional)"),
    starts_with: str = Query("", description="Prefix for each combination"),
    separator: str = Query("", description="Separator between words"),
    ends_with: str = Query("", description="Suffix for each combination"),
    min_length: int = Query(1, description="Minimum length of generated domains", ge=1, le=63),
    max_length: int = Query(63, description="Maximum length of generated domains", ge=1, le=63),
    limit: int = Query(1000, description="Maximum number of results to return", ge=1, le=10000),
    random_order: bool = Query(False, description="Whether to randomize the order of results")
):
    """
    Generate domain name combinations based on selected dictionaries and formatting options.
    Both dictionaries are optional - if not provided, empty strings will be used.
    Length filtering is applied to the final generated domains.
    Results are limited to prevent excessive response sizes.
    Random ordering can be applied to get varied results.
    """
    results = search_words(dict1, dict2, starts_with, separator, ends_with, min_length, max_length, limit, random_order)
    return results

@app.get("/dictionaries")
async def get_available_dictionaries():
    """
    Get list of available dictionaries with their names and sample content.
    """
    dict_info = {}
    for name, dict_list in DICT_MAPPING.items():
        dict_info[name] = {
            "name": name,
            "count": len(dict_list),
            "sample": dict_list[:5] if len(dict_list) > 5 else dict_list
        }
    return dict_info

@app.get("/", response_class=HTMLResponse)
async def serve_index():
    """
    Serve the main HTML page.
    """
    try:
        with open("index.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="<h1>Index.html not found</h1>", status_code=404)

@app.get("/app.js")
async def serve_js():
    """
    Serve the JavaScript file.
    """
    try:
        return FileResponse("app.js", media_type="application/javascript")
    except FileNotFoundError:
        return HTMLResponse(content="app.js not found", status_code=404)

@app.get("/test.html", response_class=HTMLResponse)
async def serve_test():
    """
    Serve the test HTML page.
    """
    try:
        with open("test.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="<h1>test.html not found</h1>", status_code=404)

@app.get("/debug.html", response_class=HTMLResponse)
async def serve_debug():
    """
    Serve the debug HTML page.
    """
    try:
        with open("debug.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="<h1>debug.html not found</h1>", status_code=404)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
