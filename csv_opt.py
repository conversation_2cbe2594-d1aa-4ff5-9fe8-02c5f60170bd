#!/usr/local/bin/python3
# -*- coding: UTF-8 -*-

# 导入 csv 库
import csv


def read_csv():

    # 以读方式打开文件
    with open("data.csv", mode="r", encoding="utf-8-sig") as f:

        # 基于打开的文件，创建csv.reader实例
        reader = csv.reader(f)
        header = next(reader)

        # 逐行获取数据，并输出
        for row in reader:
            print("{}{}: {}={}, {}={}, {}={}".format(header[0], row[0],
                                                     header[1], row[1],
                                                     header[2], row[2],
                                                     header[3], row[3]))


def write_csv():

    # 创建列表，保存header内容
    header_list = ["设备编号", "温度", "湿度", "转速"]

    # 创建列表，保存数据
    data_list = [
        [0, 31, 20, 1000],
        [1, 30, 22, 998],
        [2, 32, 33, 1005]
    ]

    # 以写方式打开文件。注意添加 newline=""，否则会在两行数据之间都插入一行空白。
    with open("new_data.csv", mode="w", encoding="utf-8-sig", newline="") as f:

        # 基于打开的文件，创建 csv.writer 实例
        writer = csv.writer(f)

        # 写入 header。
        # writerow() 一次只能写入一行。
        writer.writerow(header_list)

        # 写入数据。
        # writerows() 一次写入多行。
        writer.writerows(data_list)


if __name__ == '__main__':
    write_csv()
