#!/usr/local/bin/python3
# -*- coding: UTF-8 -*-

import time
import socket
from selenium import webdriver


from selenium import webdriver

options = webdriver.ChromeOptions()
options.add_argument("cookie: smidV2=20220804144606080a81defdb79aa22ff0868f5ea1909f009c98f4f4c321a40; gid.sig=Ntm_3XhSRXUngpy0XhA6R8V0xj8KXdilpOVWjZa4XWI; gid.sign.sig=Qz3jReJxFHyTwOrQPwZlooeNCrCScdXY84F0qJ44jVI; gid.ss=gSMQ9UOnDuZwH2oRGJG6BW6e4grs67TaYpnrW+8Wmd1ZqyXSNqCFDuZYMu6sUTR5; timestamp2=1673244753338ba9e48962ced7f88e76e93cdc6ac257e8560997f330164d94e; timestamp2.sig=JYkOlKcrwWh-qaHb7Ta_iVT-QnNih-V-RUADafFv56g; xhsTrackerId=c742e496-fea3-4734-9db9-0218e7232570; xhsTrackerId.sig=h24safGdGD8NYki6IH2zrSjRuBN99HW2RvX0JjACEyo; a1=186259cc5depektzdoxj7vjqo3hwhxd998n2e7lq630000409230; webId=c79d21b251cd257052271112826851d4; gid=yYKJ2jSSYSj8yYKJ2jSS2YW1fdUdT9uflCVWIAKhVAlqxJq8ExCfjd88848jJq88ijyyKi8y; gid.sign=KFtveGk+iGViq9C9rRmp/ysJFjY=; web_session=030037a4d3ff41f01df579ed98244a552da72b; webBuild=2.0.7; xsecappid=xhs-pc-web; websectiga=cffd9dcea65962b05ab048ac76962acee933d26157113bb213105a116241fa6c; sec_poison_id=e2a409ab-66c5-45c4-9a32-81cf885c9788; extra_exp_ids=yamcha_0327_exp,h5_1208_exp3,ques_clt2; extra_exp_ids.sig=ETM51AFqVyLPOioG2x0qNaEzMLVwrEIN37uTpfkLqxc; xhsTracker=url=explore&searchengine=baidu; xhsTracker.sig=u1cFYHAwm89lKbFLL1Y8vp9JcskioXWTa56RKaAB2ys")
options.add_argument("header2: value2")

# create a new Chrome browser instance
driver = webdriver.Chrome(options=options)

# navigate to a webpage
driver.get("https://www.xiaohongshu.com/explore/6304351d000000001800f59e")
time.sleep(10)

# execute JavaScript code to get the value of a variable
my_var = driver.execute_script("return window.__INITIAL_STATE__.note.note._value.imageList;")

# print the value of the variable
print(my_var)

# close the browser
driver.quit()

