import requests
import xml.etree.ElementTree as ET
from email.utils import parsedate_to_datetime
import pytz
import notice
import re
import time
from datetime import datetime
import twitter_api
import util


def now_str():
    now = datetime.now()
    return now.strftime('%Y-%m-%d %H:%M:%S')


def today_str():
    now = datetime.now()
    return now.strftime('%Y-%m-%d')


def is_today(date_str):
    date = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
    # 获取今天的日期
    today = datetime.now()
    # 比较年、月、日是否相等
    return date.year == today.year and date.month == today.month and date.day == today.day


def format_datetime(datetime_str):
    # 解析 GMT 时间字符串
    gmt_time = parsedate_to_datetime(datetime_str)

    # 创建一个时区对象代表中国时区
    china_tz = pytz.timezone('Asia/Shanghai')

    # 将 GMT 时间转换为中国时间
    china_time = gmt_time.astimezone(china_tz)

    # 将时间对象格式化为字符串
    china_time_str = china_time.strftime('%Y-%m-%d %H:%M:%S')

    return china_time_str


def replace_url(post):
    for key in post.keys():
        if isinstance(post[key], str):
            post[key] = post[key].replace('http://nitter.io.lol/', 'https://twitter.com/')
    return post


def convert_html_to_text(html):
    # 查找所有的 a 标签，并将其替换为 href 的值
    html = re.sub(r'<a href="(.*?)".*?>(.*?)</a>', r'\1', html)

    # 移除所有的 HTML 标签
    text = re.sub(r'<.*?>', '', html)

    return text


def get_tweet_id(string):
    match = re.findall(r'\d+', string)
    if len(match) == 1:
        return match[0]
    return False


def run_task(id_list, keywords):

    # id interval = push_interval * len(post), is random
    push_interval = 10
    api_interval = 900

    while True:
        # cache
        filename = 'twitter_post.json'
        tweet_file = 'twitter_tweet_count'
        cache_map = util.load_dict(filename)
        cache_tweet = util.load_dict(tweet_file)

        # 循环遍历 ID 列表
        for xid in id_list:

            url = f'https://nitter.io.lol/{xid}/rss'

            # 发送 GET 请求到指定的 URL
            response = requests.get(url)

            # 将响应内容解析为 XML
            root = ET.fromstring(response.content)

            # 遍历 XML 中的每一个 item
            for item in root.iter('item'):
                # 对于每一个 item，我们创建一个空字典来存储它的数据
                post = {}
                # 遍历 item 的所有子元素
                for child in item:
                    # 我们使用子元素的 tag 作为字典的键，子元素的 text 作为字典的值
                    post[child.tag] = child.text

                # format
                post['pubDate'] = format_datetime(post['pubDate'])
                post['creator'] = post['{http://purl.org/dc/elements/1.1/}creator']
                post['id'] = get_tweet_id(post['guid'])

                # check date
                if not is_today(post['pubDate']):
                    continue

                if 'title' not in post or post['title'] is None:
                    continue
                # check keywords
                kw_list = []
                for keyword in keywords:
                    if keyword.lower() in post['title'].lower():
                        kw_list.append("<a href='https://twitter.com/search?q=" + keyword + "'>#" + keyword + "</a>")
                if len(kw_list) == 0:
                    continue

                # check cache
                if post['guid'] in cache_map:
                    continue

                # only cache posts with which contains keywords
                cache_map[post['guid']] = ''

                post = replace_url(post)
                post_text = (post['title']
                             + '\n\nAuthor: <a href="https://twitter.com/'+post['creator']+'">' + post['creator']
                             + '</a> | <a href="'+post['link']+'">Read More >>></a>'
                             + '\nTags: ' + ', '.join(kw_list)
                             + '\nDate: ' + post['pubDate'])

                # print('push message: ', post_text)
                notice.push_message('new tweet', post_text)

                # tweet
                tweet_cache_key = today_str()
                if tweet_cache_key not in cache_tweet:
                    cache_tweet[tweet_cache_key] = 0

                if cache_tweet[tweet_cache_key] < 45:  # limit 50 per day, 1500 per month
                    twitter_api.tweet(post['title'], post['id'])
                    cache_tweet[tweet_cache_key] += 1
                    util.cache_dict(cache_tweet, tweet_file)

                # push message interval
                time.sleep(push_interval)

            # cache per id
            util.cache_dict(cache_map, filename)

        # api limit
        time.sleep(api_interval)


if __name__ == '__main__':

    xid_list = [
        'CoinDesk', 'CoinMarketCap', 'BNBCHAIN', 'gate_id', 'gate_io', 'okx', 'okxweb3', 'netswapofficial',
        'dexscreener', 'dYdX', 'JupiterExchange', 'DeBankDefi', 'HTX_Global', 'CoinbaseExch',
        'kucoincom', 'CoinDesk', 'binance', 'BinanceLabs', # ex, dex
        'xpet_tech', 'protocol_vega', 'MetisGovernance', 'nfprompt', 'IPFS', 'SuiNetwork', '0xPolygonLabs',
        'jito_labs', 'Optimism', 'zksync', 'FilecoinTLDR', 'iotex_io', 'realMatrixport', 'jito_sol', 'pepecoineth',
        'ensdomains', 'cryptocishanjia', 'realMatrixport', 'kusamanetwork', 'MetisDAO', 'aave', 'AlgoFoundation',
        'FantomFDN', 'FTX_Official', 'SeiNetwork', 'iota', 'avax', 'Polkadot', '0xPolygon', 'chainlink', 'cosmos',
        'Uniswap', 'NEARProtocol', 'Filecoin', 'solana', 'nansen_ai', 'litecoin', 'cryptocom',
        'Bitcoin', 'MetaMask', 'Shibtoken', 'PancakeSwap', 'cz_binance', 'coinbase', 'coingecko',
        'FilFoundation', 'arbitrum', 'Injective_', 'helium', 'memecoin', 'Ripple', 'dogecoin',  # official
        'BRC20com', 'dfinity', 'TechFlowPost', 'BitcoinMagazine', 'protocollabs', 'ProtoResearch',  # magazine, research
        'coolish', 'VitalikButerin', 'brian_armstrong', '100trillionUSD',  'elonmusk', 'blockchain', 'youcai1688', 'ForbesCrypto',
        'Cryptosis9_OKX', 'hebi555', 'DoctorMbitcoin', 'realwuzhe', 'BTCTN', 'wublockchain12', 'trondao', 'CoinDeskPodcast',
        'BlackRock', 'Grayscale', 'Memeland'
    ]
    filter_keywords = ['launched', 'unlocked', 'BRC-20', 'listing', 'airdrop', 'give away']

    try:
        run_task(xid_list, filter_keywords)
    except Exception as e:
        print('error', e)
        # util.restart_process()

    # test_list = ['CoinDesk']
    # test_keywords = ['ETF']
    # run_task(test_list, test_keywords)





