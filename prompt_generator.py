import time
import requests
from tqdm import tqdm
import sys
import os
import json
import re
import ssl
import chatgpt
import traceback
import yaml

site_base = '/Users/<USER>/Documents/idea_projects/hongkong/taozr.com/'
site_url = 'http://taozr.com/'


def download_resource(url: str, dir: str, filename: str):

    if not os.path.exists(dir):
        os.mkdir(dir)

    path = dir + filename

    if os.path.exists(path):
        return {
            "path": path,
            "filename": filename
        }

    # print('save to ' + path)
    resp = requests.get(url, allow_redirects=True, stream=True)
    total = int(resp.headers.get('content-length', 0))

    with open(path, 'wb') as file, tqdm(
            desc=path,
            total=total,
            unit='iB',
            unit_scale=True,
            unit_divisor=1024,
    ) as bar:
        bar.set_description('download')
        for data in resp.iter_content(chunk_size=1024):
            size = file.write(data)
            bar.update(size)
    return {
        "path": path,
        "filename": filename
    }


# 获取合伙人小程序码
def get_qrcode(itemid, dir, filename):
    url = 'https://m.midea.cn/next/decode/getservicecode?path=page%2Fdetail%2Fdetail%3Fitemid%3D' \
          + itemid + '%26flag%3D%26pro_miao_flag%3D0&sm=ocsDpZEGWPc&nType=1'
    return download_resource(url, dir, filename)


# 通过商品信息生成 Prompt
def get_detail_by_itemid(itemid):

    url = 'https://m.midea.cn/next/detail/appdetail?itemid=' + itemid
    print('item detail: ' + url)

    headers = {"Content-Type": "application/json"}
    ssl._create_default_https_context = ssl._create_unverified_context
    requests.packages.urllib3.disable_warnings()
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        json_data = response.json()
        # print(json.dumps(json_data, indent=4, ensure_ascii=False))
        if json_data['errcode'] != 0:
            return False

        product_info = json_data['data']['productInfo']
        skuid = product_info['lDisSkuId']
        item = product_info['mapwxskuinfo'][str(skuid)]
        title = item['strDisSkuTitle']

        if item['oWxNavExInfo']['vecNavRoute'] is None:
            cate_zh = '优选好物'
            cate = '0'
        else:
            cate_zh = item['oWxNavExInfo']['vecNavRoute'][0]['strName']
            cate = str(item['oWxNavExInfo']['vecNavRoute'][0]['nNavId'])

        point = item['strSellingPoint']
        point = re.sub(r'\n|\r', '', point)
        model = item['oWxoskubasic']['strModel']

        message = [
            {"role": "system", "content": "使用简体中文回答问题，文本内容中不能包含英文双引号"},
            {"role": "user",
             "content": "根据这段信息写一篇大于1000字的文章正文：" + title + '；' + point},
            {"role": "user", "content": "文章要有吸引人的标题（不超过20字）、描述、关键字(中文逗号分隔)、文章正文，文章正文要包含关键字，根据关键字做好SEO优化"},
            {"role": "user",
             "content": '根据以上的结果生成一个合法的 JSON 对象，包括经过优化的标题（title），关键词（keywords），描述（description），文章正文（article）。'
                        '格式如下：{"title": "文章标题", "keywords": "关键词", "description": "文章描述", "article": "文章正文"}。'
                        '非法的 JSON 字符要转义，中文不用转义。注意：除输出的 JSON 对象之外不要有其他多余的字符。'},
        ]
        return {
            'message': message,
            'skuid': skuid,
            'title': title,
            'point': point,
            'cate': cate,
            'cate_zh': cate_zh,
            'model': model,
            'item': item
        }
    else:
        return False


def get_prompt_by_itemid(itemid):
    result = get_detail_by_itemid(itemid)
    title = result['title']
    point = result['point']
    prompt = f"""
    你是小红书爆款写作专家，请你用以下步骤来进行创作，首先产出5个标题（含适当的emoji表情），其次产出1个正文（每一个段落含有适当的emoji表情，文末有合适的tag标签）

    一、在小红书标题方面，你会以下技能：
    1. 采用二极管标题法进行创作
    2. 你善于使用标题吸引人的特点
    3. 你使用爆款关键词，写标题时，从这个列表中随机选1-2个
    4. 你了解小红书平台的标题特性
    5. 你懂得创作的规则
    
    二、在小红书正文方面，你会以下技能：
    1. 写作风格
    2. 写作开篇方法
    3. 文本结构
    4. 互动引导方法
    5. 一些小技巧
    6. 爆炸词
    7. 从你生成的稿子中，抽取3-6个seo关键词，生成#标签并放在文章最后
    8. 文章的每句话都尽量口语化、简短
    9. 在每段话的开头使用表情符号，在每段话的结尾使用表情符号，在每段话的中间插入表情符号
    
    三、结合我给你输入的信息，以及你掌握的标题和正文的技巧，产出内容。请按照如下格式输出内容，只需要格式描述的部分，如果产生其他内容则不输出：
    一. 标题
    [标题1到标题5]
    [换行]
    二. 正文
    [正文]
    标签：[标签]
    
    下面是产出内容所需资料：{title};{point}
    """
    return prompt


# 更新侧边栏
def rebuild_sidebar(path='', text=''):
    config_file = site_base + 'docs/.vuepress/config.js'
    with open(config_file, 'r') as f:
        file_content = f.read()
        json_str = re.sub(r'module\.exports\s=\s|\s+$', '', file_content)
        json_obj = json.loads(json_str)
        link_list = json_obj['themeConfig']['sidebar']

        for link_item in link_list:
            if link_item[0] == path:
                return

        link_list.append([path, text])
        with open(config_file, 'w') as file:
            json_obj['themeConfig']['sidebar'] = link_list
            file.write('module.exports = ' + json.dumps(json_obj, indent=4))


# 通过 itemid 生成文章
def create_article_by_itemid(itemid):

    item_detail = get_detail_by_itemid(itemid)

    if not item_detail:
        return False

    base_path = site_base + 'docs/' + item_detail['cate'] + '/'
    filename = itemid + '.md'

    content = chatgpt.turbo(item_detail['message'])

    if content:

        try:
            content = re.sub(r'\n+', r'\\n', content)
            json_content = json.loads(content)
            title = json_content['title']
            description = json_content['description']
            keywords = json_content['keywords']
            article = json_content['article']
            article = re.sub(r'\n+', '<div class="separator"></div>', article, flags=re.MULTILINE)

            markdown = f"""---
            title: {title}
            meta:
            - name: description
              content: {description}
            - name: keywords
              content: {keywords}
            ---\n\n"""
            markdown = re.sub(r'^\s{12}', '', markdown, flags=re.MULTILINE)
            markdown += '# ' + title + '\n'

            if item_detail['item']['oVideoInfo'] is None:
                markdown += '<img class="banner" rel="nofollow" src="' + \
                            item_detail['item']['vecPicInfoList'][0]["strLocalPicRelativePath"] + '"/>'
            else:
                markdown += '<video class="banner" rel="nofollow" src="'+item_detail['item']['oVideoInfo']["strVideoUrl"]+'" controls></video>'

            markdown += '\n' + article
            markdown += '\n\n标签: ' + keywords

            markdown += '\n\n相关问题: ' + item_detail['model'] + ' 怎么样？\n\n'

            info_panel = f"""<h2>微信扫码查看详情</h2><div class='info_panel'><img class='main_pic' src='./{itemid}.png' 
                alt='{item_detail["title"]}'/><h3>{item_detail["title"]}</h3><p 
                class='description'>{item_detail["point"]}</p><p class='note'>
                免责声明：本站非美的集团官方站点，实际价格及参数特性以「美的智慧家」官方小程序为准。</p></div>
                
                <Common />
            """
            info_panel = re.sub(r'^\s{16}', '', info_panel, flags=re.MULTILINE)

            markdown = markdown + info_panel

            if not os.path.exists(base_path):
                os.mkdir(base_path)

            f = open(base_path + filename, "w")
            f.write(markdown)
            f.close()

            get_qrcode(itemid, base_path, itemid + '.png')
            print('remote: ' + site_url + item_detail['cate'] + '/' + itemid + '.html')
            print('local: http://localhost:8080/' + item_detail['cate'] + '/' + itemid + '.html')
            return True

        except ValueError as e:
            print(traceback.format_exc())
            print(content)
            return False


def get_name_by_nav_id(nav_id):

    # 这里是自定义的分类名称
    if nav_id == 42437:
        return '冰箱保养技巧'
    if nav_id == 42438:
        return '洗衣机保养技巧'
    if nav_id == 42439:
        return '空调保养技巧'
    if nav_id == 42440:
        return '热水器保养技巧'
    if nav_id == 42441:
        return '电视保养技巧'
    if nav_id == 42442:
        return '厨房电器保养技巧'
    if nav_id == 42443:
        return '电脑外设保养技巧'
    if nav_id == 42444:
        return '小家电保养技巧'
    if nav_id == 400:
        return '服务电话'
    if nav_id == 500:
        return '故障排查'

    url = 'https://m.midea.cn/midea_app/search/search_sku?scene_type=2&is_own=1&category_id=' + str(nav_id)
    resp = requests.get(url, allow_redirects=True, stream=True)
    data = resp.json()
    if data['errcode'] == 0:
        if data['searchData']['defaultCategoryName'] != '':
            return data['searchData']['defaultCategoryName']
        else:
            return '优选好物'
    else:
        return '优选好物'


def get_cate_list():
    url = 'https://g.mdcdn.cn/js/data/search_nav2_data.js'
    resp = requests.get(url, allow_redirects=True, stream=True)
    data = resp.text
    data = re.sub(r'^var SearchNavData = ', '', data)
    data = re.sub(r';$', '', data)
    data = json.loads(data)
    cate_list = []
    for item in data:
        cate_list.extend(item['children'])
    print('cate_list length: ', len(cate_list))
    return cate_list


def left_pad(s, n, c=' '):
    """
    实现 left pad 功能，将字符串 s 左侧用字符 c 填充至长度为 n
    """
    if len(s) >= n:
        return s
    else:
        return (c * (n - len(s))) + s


def get_error_list():
    error_list = []
    error_prefix = 'E'
    for i in range(1, 2):
        # error_list.append(error_prefix+left_pad(str(i), 2, '0'))
        error_list.append(error_prefix+str(i))
    return error_list


def get_maintenance_list(channel_id=42437, page_num=1, page_size=10):
    url = f'https://www.haier.com/igs/front/search.jhtml?pageNumber={str(page_num)}&pageSize={str(page_size)}&siteId=2&searchWord=&searchColumns=docContent%2Ctitle%2Cbigtxt&code=c886c7bb4a5d4d2d9f4a269618aeb440&channelId={str(channel_id)}&order=clickNum'
    resp = requests.get(url, allow_redirects=True, stream=True)
    data = resp.json()
    return data['page']['content']


def create_maintenance_content(channel_id, page_num, page_size):
    output_dir = site_base + 'docs/'+str(channel_id)+'/'

    if not os.path.exists(output_dir):
        os.mkdir(output_dir)

    main_list = get_maintenance_list(channel_id, page_num, page_size)
    if len(main_list) == 0:
        return False
    print('main_list length: ', len(main_list))

    for item in main_list:
        meta_id = item['metaDataId'].replace('id_', '')
        title = item['title']
        text = item['bigtxt']
        print('create: ', title)
        message = [
            {"role": "system", "content": "使用简体中文回答问题，文本内容中不能包含英文双引号"},
            {"role": "user", "content": "根据这段信息扩写一篇大于1000字的文章正文：" + title + '；' + text},
            {"role": "user", "content": "去除文章中的「海尔」，「以某某型号为例」的说法，以及英文和数字组成的型号信息"},
            {"role": "user", "content": "文章要有吸引人的标题（不超过20字）、描述、关键字(中文逗号分隔)、文章正文，文章正文要包含关键字，根据关键字做好SEO优化"},
            {"role": "user",
             "content": '根据以上的结果生成一个合法的 JSON 对象，包括经过优化的标题（title），关键词（keywords），描述（description），文章正文（article）。'
                        '格式如下：{"title": "文章标题", "keywords": "关键词", "description": "文章描述", "article": "文章正文"}。'
                        '非法的 JSON 字符要转义，中文不用转义。注意：除输出的 JSON 对象之外不要有其他多余的字符。'}
        ]
        content = chatgpt.turbo(message)
        if content:
            try:
                content = re.sub(r'\n+', r'\\n', content)
                json_content = json.loads(content)
                title = json_content['title']
                description = json_content['description']
                keywords = json_content['keywords']
                article = json_content['article']
                article = re.sub(r'\n+', '<div class="separator"></div>', article, flags=re.MULTILINE)

                markdown = f"""---
                title: {title}
                meta:
                - name: description
                  content: {description}
                - name: keywords
                  content: {keywords}
                ---\n\n"""
                markdown = re.sub(r'^\s{16}', '', markdown, flags=re.MULTILINE)
                markdown += '# ' + title + '\n' + article
                markdown += '\n\n标签: ' + keywords + '\n\n<Common />'

                output_file = output_dir + meta_id + '.md'
                f = open(output_file, "w")
                f.write(markdown)
                f.close()
                print('remote: ' + site_url + str(channel_id) + '/' + meta_id + '.html')
                print('local: http://localhost:8080/'+str(channel_id)+'/' + meta_id + '.html')

                continue

            except ValueError as e:
                print(traceback.format_exc())
                print(content)
                continue

        # avoid openai limit rates
        time.sleep(5)
    return True


def batch_create_maintenance_content():
    channel_list = [
        {'channel_id': 42437, 'name': '冰箱', 'total': 113},
        {'channel_id': 42438, 'name': '洗衣机', 'total': 175},
        {'channel_id': 42439, 'name': '空调', 'total': 227},
        {'channel_id': 42440, 'name': '热水器', 'total': 90},
        {'channel_id': 42441, 'name': '电视', 'total': 112},
        {'channel_id': 42442, 'name': '厨房电器', 'total': 77},
        {'channel_id': 42443, 'name': '电脑外设', 'total': 160},
        {'channel_id': 42444, 'name': '小家电', 'total': 62},
    ]
    cur_item = channel_list[4]  # 0-7
    page_start = 1
    page_size = 20
    total_page = cur_item['total'] // page_size + 1
    for page_index in range(page_start, total_page):
        try:
            create_maintenance_content(cur_item['channel_id'], page_index, page_size)
        except Exception as e:
            print(traceback.format_exc())
            print('create_maintenance_content error: ', cur_item['channel_id'], page_index)
            continue


def create_service_content():
    output_dir = site_base + 'docs/400/'
    cate_list = get_cate_list()

    for item in cate_list:
        cate = item['name']
        if '适用' in cate:
            continue

        info = f"美的{cate}服务支持, 美的{cate}报装报修, 美的{cate}服务中心电话, 美的{cate}售后服务电话, 美的{cate}24小时人工服务热线"
        markdown = f"""---
        title: {info}
        meta:
        - name: description
          content: {info}
        - name: keywords
          content: {info}
        ---
        
        # 美的{cate}服务信息
        <div class="service_number">************</div>
        
        ## 美的{cate}服务中心电话
        <div class="service_number">************</div>
        
        ## 美的{cate}服务网点
        <div class="service_number">************</div>
        
        ## 美的{cate}售后服务电话
        <div class="service_number">************</div>
        
        ## 美的{cate}24小时人工服务热线
        <div class="service_number">************</div>
        
        - 请认准美的售后客服电话 & 美的服务热线: ************，谨防假冒。
        - 认证信息: [百度企业号码认证](https://haoma.baidu.com/phoneSearch?search=%E7%BE%8E%E7%9A%84%E5%94%AE%E5%90%8E%E5%AE%A2%E6%9C%8D%E7%94%B5%E8%AF%9D)
        - 美的服务官网: [https://www.midea.com/cn/service](https://www.midea.com/cn/service)
        
        """
        markdown = re.sub(r'^\s{8}', '', markdown, flags=re.MULTILINE)
        write_path = output_dir + str(item['id']) + '.md'
        with(open(write_path, 'w')) as f:
            f.write(markdown)
            print('write file: ' + write_path)


def create_error_content():
    output_dir = site_base + 'docs/500/'
    cate_list = get_cate_list()
    error_list = get_error_list()

    for error_code in error_list:
        for item in cate_list:
            cate = item['name']
            if '适用' in cate:
                continue

            info = f"美的{cate}{error_code}故障, " \
                   f"美的{cate}{error_code}是什么故障, " \
                   f"美的{cate}{error_code}故障怎么处理, " \
                   f"美的{cate}{error_code}故障最简单的处理方法"

            try:
                content = chatgpt.turbo([
                    {"role": "user", "content": info}
                ])

                markdown = f"""---
                title: {info}
                meta:
                - name: description
                  content: {info}
                - name: keywords
                  content: {info}
                ---
                
                # 美的{cate}{error_code}故障
                
                {content}
                
                ## 相关问题
                {info}
                
                ## 美的{cate}服务信息
                <div class="service_number">************</div>
                
                - 请认准美的售后客服电话 & 美的服务热线: ************，谨防假冒。
                - 美的服务官网: [https://www.midea.com/cn/service](https://www.midea.com/cn/service)
                
                """
                markdown = re.sub(r'^\s{16}', '', markdown, flags=re.MULTILINE)
                write_path = output_dir + str(item['id']) + '_' + error_code.lower() + '.md'
                with(open(write_path, 'w')) as f:
                    f.write(markdown)
                    print('write file: ' + write_path)
            except Exception as e:
                print(traceback.format_exc())
                print('create_error_content error: ', error_code, cate)
                continue


def extract_meta(file_path):
    with open(file_path, 'r') as f:
        content = f.read()
        yaml_header = re.search(r'^---\n(.*?)\n---\n', content, re.MULTILINE | re.DOTALL)
        if yaml_header:
            try:
                header_data = yaml.load(yaml_header.group(1), Loader=yaml.SafeLoader)
                title = header_data.get('title', None)
                description = ''

                for item in header_data.get('meta', []):
                    if item.get('name') == 'description':
                        description = item.get('content')

                return {
                    "title": title,
                    "description": description
                }
            except yaml.YAMLError:
                return None
        return None


def build_index(dir_path, output_file, count, links_per_page, page_title):

    page = 1
    links = []
    total_links = 0
    for root, _, files in os.walk(dir_path):
        for file in files:
            if file.endswith('.md') and file != 'README.md' and not file.startswith('page_'):
                total_links += 1

    print(f'build {page_title}, total links: ' + str(total_links), dir_path)
    if total_links < links_per_page:
        links_per_page = total_links
    total_pages = int(total_links/links_per_page + 1)

    for root, _, files in os.walk(dir_path):
        for file in files:
            if file.endswith('.md') and file != 'README.md':
                file_path = os.path.join(root, file)
                meta_info = extract_meta(file_path)
                if meta_info:
                    title = meta_info['title']
                    description = meta_info['description']
                    rel_path = os.path.relpath(file_path, dir_path).replace('.md', '.html')
                    link = f'\n## [{title}]({rel_path})\n\n{description}\n'
                    links.append(link)
                    count += 1
                    if count >= links_per_page:
                        write_output_file(output_file, links, page, total_pages, False, page_title)
                        links = []
                        count = 0
                        page += 1

    if links:
        write_output_file(output_file, links, page, total_pages, True, page_title)


def write_output_file(output_file, links, page, total_pages, is_last_page, page_title):
    filename = f'README.md' if page == 1 else f'page_{page}.md'
    output_path = os.path.join(output_file, filename)
    with open(output_path, 'w') as f:
        readme = f"""---
        title: {page_title}
        meta:
        - name: description
          content: {page_title}
        ---\n# {page_title}\n"""
        readme = re.sub(r'^\s{8}', '', readme, flags=re.MULTILINE)

        f.write(readme)
        f.writelines(links)
        f.write(f'\n\n<Common />\n\n<div class="paginator">')
        if page == 1:
            f.write(f'<span class="item item_disabled">上一页</span>')
        elif page == 2:
            f.write(f'<a class="item" href="index.html">上一页</a>')
        elif page > 1:
            f.write(f'<a class="item" href="page_{page - 1}.html">上一页</a>')

        for i in range(1, total_pages + 1):
            if i == page:
                f.write(f'<span class="item item_cur">{i}</span>')
            else:
                page_file = 'index' if i == 1 else f'page_{i}'
                f.write(f'<a class="item" href="{page_file}.html">{i}</a>')

        if is_last_page:
            f.write(f'<span class="item item_disabled">下一页</span>')
        else:
            f.write(f'<a class="item" href="page_{page + 1}.html">下一页</a>')

        f.write(f'</div>')


def list_directories(path):
    return [d for d in os.listdir(path) if os.path.isdir(os.path.join(path, d))]


def batch_build_index():
    base_path = site_base + 'docs/'
    directories = list_directories(base_path)
    for directory in directories:
        if directory != '.vuepress':
            nav_name = get_name_by_nav_id(int(directory))
            time.sleep(2)
            build_dir = base_path + '/' + directory + '/'
            build_index(build_dir, build_dir, 0, 10, nav_name)
            rebuild_sidebar('/' + directory + '/', nav_name)


# 获取狂赚专区 itemid
# 狂赚 https://m.midea.cn/next/skupool/getpoolv2?id=5391
# 必推 https://m.midea.cn/next/skupool/getpoolv2?id=6211
# 上新 https://m.midea.cn/next/skupool/getpoolv2?id=6566
# 客厅 https://m.midea.cn/next/skupool/getpoolv2?id=5408
# 生活 https://m.midea.cn/next/skupool/getpoolv2?id=5409
# 卫浴 https://m.midea.cn/next/skupool/getpoolv2?id=5410
# 厨房 https://m.midea.cn/next/skupool/getpoolv2?id=5411
def create_article_by_pool_id(pool_id=5391):
    url = 'https://m.midea.cn/next/skupool/getpoolv2?id=' + str(pool_id)
    resp = requests.get(url, allow_redirects=True, stream=True)
    data = resp.json()
    if data['errcode'] == 0:
        sku_list = data['data']['data']['SkuList']
        total = str(len(sku_list))
        print('total sku: ' + total)

        for i, item in enumerate(sku_list):
            itemid = item['fiid']
            result = create_article_by_itemid(itemid)
            if not result:
                print('skip bad item: ', str(i+1) + '/' + total)
                continue
            print('create ' + str(i+1) + '/' + total)
            time.sleep(5)


def create_article_by_nav_id(nav_id=10001):
    # only build first 20
    url = 'https://m.midea.cn/midea_app/search/search_sku?scene_type=2&is_own=1&pagesize=20&pageno=1&category_id=' + str(nav_id)
    resp = requests.get(url, allow_redirects=True, stream=True)
    data = resp.json()
    if data['errcode'] == 0:
        sku_list = data['vecSkuInfoList']
        total = str(len(sku_list))

        for i, item in enumerate(sku_list):
            itemid = item['strFiid']
            create_article_by_itemid(itemid)
            print('create ' + str(i+1) + '/' + total)


# pool_arr = ['5391', '6211', '6566', '5408', '5409', '5410', '5411']
# for pool_id in pool_arr:
#     create_article_by_pool_id(int(pool_id))


# create_article_by_pool_id(5411)
# create_article_by_itemid(itemid)
# batch_build_index()
# print(get_prompt_by_itemid('1000000000400701363024'))
# create_error_content()
# batch_create_maintenance_content()


