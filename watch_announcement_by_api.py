import requests
import json
import datetime
import time
import notice
import re
import gate
import util


def format_time(timestamp_millis):
    # Unix时间戳（毫秒）
    timestamp = timestamp_millis / 1000
    dt_obj = datetime.datetime.fromtimestamp(timestamp)

    # 减去8小时
    # dt_obj_minus_8 = dt_obj - datetime.timedelta(hours=8)

    return dt_obj.strftime('%Y-%m-%d %H:%M:%S')


def is_today(timestamp):
    # 将毫秒时间戳转换为秒时间戳，并创建一个 datetime 对象
    dt = datetime.datetime.fromtimestamp(timestamp / 1000)

    # 获取今天的日期
    today = datetime.datetime.now()

    # 检查时间戳的日期是否是今天
    return dt.date() == today.date()


def get_announcement():
    # api = 'https://www.binance.com/bapi/composite/v3/public/market/notice/get?page=9&rows=1'
    api = "https://www.binance.com/bapi/composite/v3/public/market/notice/get?page=1&rows=1"
    headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.42(0x18002a2a) NetType/4G Language/zh_CN',
        "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "accept-language": "zh,en-US;q=0.9,en;q=0.8,zh-CN;q=0.7",
        "cache-control": "max-age=0",
        "sec-ch-ua": "\"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"macOS\"",
        "sec-fetch-dest": "document",
        "sec-fetch-mode": "navigate",
        "sec-fetch-site": "none",
        "sec-fetch-user": "?1",
        "upgrade-insecure-requests": "1"
    }

    res = requests.get(api, headers=headers)
    if res.status_code != 200:
        return

    try:
        announcement_data = json.loads(res.text)
        if announcement_data['code'] == '000000':
            item = announcement_data['data'][0]
            matches = re.findall(r'Binance Will List.*?\((.*?)\)', item['title'])
            if len(matches) == 0:
                return False
            return {
                'id': matches[0],
                'title': item['title'],
                'coin': matches[0] if len(matches) > 0 else '',
                'time': format_time(item['time']),
                'type': item['type'],  # New Cryptocurrency Listing
                'url': 'https://www.binance.com' + item['url']
            }
        else:
            return False
    except json.JSONDecodeError:
        print("JSONDecodeError: Invalid JSON string.", format_time(int(time.time() * 1000)))
        print('Headers:', res.headers)
        print('Content:', res.text)
        return False


def watch_announcement():
    # notice.push_message('new process started.', format_time(int(time.time() * 1000)))
    filename = "binance_data.json"
    cache_data = util.load_dict(filename)

    while True:
        data = get_announcement()
        if data and data['type'] == 'New Cryptocurrency Listing':
            if data['coin'] != '' and data['coin'] != cache_data['coin']:
                cache_data = data

                # place order
                created = gate.spot_trade(data['coin'] + '_USDT')

                # send message
                msg = 'SYMBOL: ' + data['coin'] + '\n发布时间: ' + data['time'] + '\n服务器时间: ' + format_time(int(time.time() * 1000))
                if created:
                    msg += '\n订单ID: ' + created.id + '\n订单状态: ' + created.status
                notice.push_message(data['title'], msg, data['url'], 'Read More >>>')

                util.cache_dict(cache_data, filename)
        time.sleep(1)


if __name__ == '__main__':
    try:
        watch_announcement()
    except Exception as e:
        print('unknown error, ', e,  format_time(int(time.time() * 1000)))
        util.restart_process()

# nohup /usr/local/bin/python3.12 /python/learn/announcement.py &
