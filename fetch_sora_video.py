import requests
import json
import util
import time


# https://madewithsora.com/
# https://api.collectui.com/sora/list?page=1
def fetch_sora_video(page):
    api = f"https://api.collectui.com/sora/list?page={page}"
    res = requests.get(api, headers={'Content-Type': 'application/json'})
    res_json = res.json()
    data = res_json['data']

    file_name = "sora_video.json"
    cache_data = util.load_dict(file_name)

    if 'data' not in cache_data:
        cache_data['data'] = []

    for item in data:
        cache_data['data'].append(item)
        print(item['author_name'])

    util.cache_dict(cache_data, file_name)


def create_index():
    file_name = "sora_video.json"
    cache_data = util.load_dict(file_name)
    data = cache_data['data']
    content = ""
    footer = ""
    for idx, item in enumerate(data):
        content += f"<div class='video_box'>\n<video autoplay loop muted src='{item['video_url']}'></video>\n"
        content += "<p>Prompt: " + item["prompt"] + "</p>\n</div>\n\n"

    with open('./sora/index.md', 'w') as f:
        f.write(content + "\n\n" + footer)


def create_post():
    file_name = "sora_video.json"
    cache_data = util.load_dict(file_name)
    data = cache_data['data']
    content = ""
    footer = ""
    for idx, item in enumerate(data):
        content += str(idx+1) + ". Prompt: " + item["prompt"] + "\n"
        footer += item["tweet_url"] + "\n"

    with open('./sora/index.md', 'w') as f:
        f.write(content + "\n\n" + footer)


def download_video():
    file_name = "sora_video.json"
    cache_data = util.load_dict(file_name)
    data = cache_data['data']
    for idx, item in enumerate(data):
        util.download_resource(item["video_url"], "./sora/", "sora_" + str(idx) + ".mp4")


create_index()
